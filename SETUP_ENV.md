# Configuración de Variables de Entorno

## Pasos para configurar Supabase

1. **Obtener las credenciales de Supabase:**
   - Ve a tu proyecto en [Supabase Dashboard](https://app.supabase.com)
   - Navega a Settings → API
   - Encontrarás dos valores importantes:
     - **Project URL**: algo como `https://ajsamgugqfbttkrlgvbr.supabase.co`
     - **anon (public) key**: una clave larga que empieza con `eyJ...`

2. **Actualizar el archivo .local:**
   ```bash
   # Abre el archivo .local en la raíz del proyecto
   # Reemplaza TU_CLAVE_ANON_DE_SUPABASE con tu clave real
   
   EXPO_PUBLIC_SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
   EXPO_PUBLIC_SUPABASE_ANON_KEY="eyJ... (tu clave completa aquí)"
   ```

3. **Reiniciar el servidor de desarrollo:**
   ```bash
   # Detén el servidor actual (Ctrl+C)
   # Vuelve a iniciarlo
   npm run ios    # o
   npm run android
   ```

## Importante

- El archivo `.local` **NO** debe subirse a git (ya está en .gitignore)
- La clave `anon` es pública y segura para usar en aplicaciones cliente
- Asegúrate de que la URL del proyecto sea correcta
- Si cambias estas variables, debes reiniciar el servidor de desarrollo

## Verificación

Para verificar que todo está configurado correctamente:

1. Intenta registrar un nuevo usuario
2. Si ves el error "Error de configuración. Contacta al soporte.", revisa:
   - Que la clave anon esté completa y correcta
   - Que la URL del proyecto sea correcta
   - Que hayas reiniciado el servidor

## Troubleshooting

Si encuentras problemas:

- **"Invalid API key"**: La clave anon es incorrecta o incompleta
- **"Network request failed"**: Verifica tu conexión a internet
- **"Project not found"**: La URL del proyecto es incorrecta

Para más ayuda, consulta la [documentación de Supabase](https://supabase.com/docs).