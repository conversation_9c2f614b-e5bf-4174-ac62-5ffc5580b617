export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      ai_analysis_cache: {
        Row: {
          analysis_type: string
          cost_usd: number | null
          created_at: string | null
          expires_at: string | null
          id: string
          input_data: Json | null
          input_hash: string
          model_used: string | null
          result: Json
          salon_id: string
          tokens_used: number | null
        }
        Insert: {
          analysis_type: string
          cost_usd?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          input_data?: Json | null
          input_hash: string
          model_used?: string | null
          result: Json
          salon_id: string
          tokens_used?: number | null
        }
        Update: {
          analysis_type?: string
          cost_usd?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          input_data?: Json | null
          input_hash?: string
          model_used?: string | null
          result?: Json
          salon_id?: string
          tokens_used?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "ai_analysis_cache_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
        ]
      }
      client_consents: {
        Row: {
          client_id: string
          consent_text: string
          consent_type: string
          created_at: string | null
          id: string
          ip_address: unknown | null
          salon_id: string
          service_id: string | null
          signature_url: string | null
          signed_at: string | null
        }
        Insert: {
          client_id: string
          consent_text: string
          consent_type: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          salon_id: string
          service_id?: string | null
          signature_url?: string | null
          signed_at?: string | null
        }
        Update: {
          client_id?: string
          consent_text?: string
          consent_type?: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          salon_id?: string
          service_id?: string | null
          signature_url?: string | null
          signed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_consents_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "client_consents_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "client_consents_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          allergies: string[] | null
          birth_date: string | null
          created_at: string | null
          created_by: string | null
          current_medications: string | null
          email: string | null
          id: string
          is_vip: boolean | null
          medical_conditions: string | null
          name: string
          notes: string | null
          phone: string | null
          salon_id: string
          tags: string[] | null
          updated_at: string | null
        }
        Insert: {
          allergies?: string[] | null
          birth_date?: string | null
          created_at?: string | null
          created_by?: string | null
          current_medications?: string | null
          email?: string | null
          id?: string
          is_vip?: boolean | null
          medical_conditions?: string | null
          name: string
          notes?: string | null
          phone?: string | null
          salon_id: string
          tags?: string[] | null
          updated_at?: string | null
        }
        Update: {
          allergies?: string[] | null
          birth_date?: string | null
          created_at?: string | null
          created_by?: string | null
          current_medications?: string | null
          email?: string | null
          id?: string
          is_vip?: boolean | null
          medical_conditions?: string | null
          name?: string
          notes?: string | null
          phone?: string | null
          salon_id?: string
          tags?: string[] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clients_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clients_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
        ]
      }
      formulas: {
        Row: {
          brand: string | null
          created_at: string | null
          created_by: string | null
          formula_data: Json
          formula_text: string
          id: string
          line: string | null
          name: string | null
          processing_time_minutes: number | null
          salon_id: string
          service_id: string | null
          technique: string | null
          total_cost: number | null
        }
        Insert: {
          brand?: string | null
          created_at?: string | null
          created_by?: string | null
          formula_data: Json
          formula_text: string
          id?: string
          line?: string | null
          name?: string | null
          processing_time_minutes?: number | null
          salon_id: string
          service_id?: string | null
          technique?: string | null
          total_cost?: number | null
        }
        Update: {
          brand?: string | null
          created_at?: string | null
          created_by?: string | null
          formula_data?: Json
          formula_text?: string
          id?: string
          line?: string | null
          name?: string | null
          processing_time_minutes?: number | null
          salon_id?: string
          service_id?: string | null
          technique?: string | null
          total_cost?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "formulas_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "formulas_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "formulas_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          barcode: string | null
          brand: string
          cost_per_unit: number | null
          created_at: string | null
          id: string
          is_active: boolean | null
          line: string | null
          minimum_stock_ml: number | null
          name: string
          sale_price: number | null
          salon_id: string
          size_ml: number
          stock_ml: number | null
          type: string | null
          updated_at: string | null
        }
        Insert: {
          barcode?: string | null
          brand: string
          cost_per_unit?: number | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          line?: string | null
          minimum_stock_ml?: number | null
          name: string
          sale_price?: number | null
          salon_id: string
          size_ml: number
          stock_ml?: number | null
          type?: string | null
          updated_at?: string | null
        }
        Update: {
          barcode?: string | null
          brand?: string
          cost_per_unit?: number | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          line?: string | null
          minimum_stock_ml?: number | null
          name?: string
          sale_price?: number | null
          salon_id?: string
          size_ml?: number
          stock_ml?: number | null
          type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "products_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          is_active: boolean | null
          permissions: string[] | null
          role: string | null
          salon_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          is_active?: boolean | null
          permissions?: string[] | null
          role?: string | null
          salon_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          is_active?: boolean | null
          permissions?: string[] | null
          role?: string | null
          salon_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
        ]
      }
      salons: {
        Row: {
          created_at: string | null
          id: string
          name: string
          owner_id: string | null
          settings: Json | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          owner_id?: string | null
          settings?: Json | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          owner_id?: string | null
          settings?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      services: {
        Row: {
          after_photos: string[] | null
          ai_analysis: Json | null
          before_photos: string[] | null
          client_id: string
          created_at: string | null
          duration_minutes: number | null
          id: string
          notes: string | null
          price: number | null
          salon_id: string
          service_date: string | null
          service_type: string
          status: string | null
          stylist_id: string
          updated_at: string | null
        }
        Insert: {
          after_photos?: string[] | null
          ai_analysis?: Json | null
          before_photos?: string[] | null
          client_id: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          notes?: string | null
          price?: number | null
          salon_id: string
          service_date?: string | null
          service_type: string
          status?: string | null
          stylist_id: string
          updated_at?: string | null
        }
        Update: {
          after_photos?: string[] | null
          ai_analysis?: Json | null
          before_photos?: string[] | null
          client_id?: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          notes?: string | null
          price?: number | null
          salon_id?: string
          service_date?: string | null
          service_type?: string
          status?: string | null
          stylist_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "services_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "services_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "services_stylist_id_fkey"
            columns: ["stylist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      stock_movements: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          notes: string | null
          product_id: string
          quantity_ml: number
          reference_id: string | null
          reference_type: string | null
          salon_id: string
          type: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          notes?: string | null
          product_id: string
          quantity_ml: number
          reference_id?: string | null
          reference_type?: string | null
          salon_id: string
          type: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          notes?: string | null
          product_id?: string
          quantity_ml?: number
          reference_id?: string | null
          reference_type?: string | null
          salon_id?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "stock_movements_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stock_movements_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stock_movements_salon_id_fkey"
            columns: ["salon_id"]
            isOneToOne: false
            referencedRelation: "salons"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const