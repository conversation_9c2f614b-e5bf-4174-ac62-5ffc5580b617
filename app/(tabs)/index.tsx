import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Platform, Modal, Alert, ActivityIndicator } from "react-native";
import { Link, router } from "expo-router";
import { Zap, Users, Package, BarChart3, Calendar, Sparkles, TrendingUp, Shield, X } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import InventoryReports from "@/components/reports/InventoryReports";
import { useServiceDraftStore } from "@/stores/service-draft-store";
import { useDashboardStore } from "@/stores/dashboard-store";

export default function HomeScreen() {
  const [showReportsModal, setShowReportsModal] = useState(false);
  const { configuration } = useSalonConfigStore();
  const { getPendingDrafts, deleteDraft } = useServiceDraftStore();
  const { metrics, isLoading, loadTodayMetrics, startAutoRefresh } = useDashboardStore();
  
  const linkStyle = Platform.select({
    web: { textDecoration: 'none' } as const,
    default: {}
  }) as any;
  
  const handleProductSelect = (productId: string) => {
    setShowReportsModal(false);
    router.push(`/inventory/${productId}`);
  };
  
  // Check for pending service drafts on mount
  useEffect(() => {
    const checkPendingDrafts = async () => {
      const drafts = getPendingDrafts();
      
      if (drafts.length > 0) {
        // Show the most recent draft
        const mostRecentDraft = drafts.sort((a, b) => 
          new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime()
        )[0];
        
        const timeAgo = getTimeAgo(mostRecentDraft.lastSaved);
        
        Alert.alert(
          "Servicio pendiente",
          `Tienes un servicio sin terminar para ${mostRecentDraft.clientName} (guardado ${timeAgo})`,
          [
            {
              text: "Descartar",
              style: "destructive",
              onPress: () => {
                deleteDraft(mostRecentDraft.id);
                console.log('[HomeScreen] Draft discarded');
              }
            },
            {
              text: "Continuar",
              onPress: () => {
                // Navigate to service with client and restore draft
                router.push({
                  pathname: "/service/new",
                  params: { 
                    clientId: mostRecentDraft.clientId,
                    restoreDraft: "true"
                  }
                });
              }
            }
          ],
          { cancelable: true }
        );
      }
    };
    
    // Delay check slightly to avoid immediate alerts
    const timer = setTimeout(checkPendingDrafts, 500);
    return () => clearTimeout(timer);
  }, []);

  // Load dashboard metrics on mount and set up auto-refresh
  useEffect(() => {
    loadTodayMetrics();
    const cleanup = startAutoRefresh();
    return cleanup;
  }, []);
  
  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const saved = new Date(date);
    const diffMs = now.getTime() - saved.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 60) {
      return `hace ${diffMins} minutos`;
    } else if (diffMins < 1440) {
      const hours = Math.floor(diffMins / 60);
      return `hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `hace ${days} día${days > 1 ? 's' : ''}`;
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Salonier</Text>
        <Text style={styles.subtitle}>Asistente de Coloración Profesional</Text>
      </View>

      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
        
        <TouchableOpacity 
          style={styles.primaryAction}
          onPress={() => router.push('/service/client-selection')}
          activeOpacity={0.9}
        >
          <View style={styles.actionIcon}>
            <Zap size={28} color="white" />
          </View>
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>Nuevo Servicio</Text>
            <Text style={styles.actionSubtitle}>Diagnóstico capilar inteligente con selección de cliente</Text>
          </View>
          <Sparkles size={24} color="white" />
        </TouchableOpacity>

        <View style={styles.secondaryActions}>
          <TouchableOpacity 
            onPress={() => router.push('/clients')}
            style={styles.secondaryAction}
          >
            <Users size={24} color={Colors.light.primary} />
            <Text style={styles.secondaryActionText}>Clientes</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            onPress={() => router.push('/inventory')}
            style={styles.secondaryAction}
          >
            <Package size={24} color={Colors.light.primary} />
            <Text style={styles.secondaryActionText}>Inventario</Text>
          </TouchableOpacity>
          
          {configuration.inventoryControlLevel !== 'solo-formulas' && (
            <TouchableOpacity 
              onPress={() => setShowReportsModal(true)}
              style={styles.secondaryAction}
            >
              <BarChart3 size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Reportes</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Resumen de Hoy</Text>
        
        <View style={styles.statsGrid}>
          <TouchableOpacity 
            style={styles.statCard}
            activeOpacity={0.7}
            onPress={() => router.push('/clients')}
          >
            <View style={styles.statIcon}>
              <Zap size={20} color={Colors.light.primary} />
            </View>
            {isLoading ? (
              <ActivityIndicator size="small" color={Colors.light.primary} style={{ marginVertical: 8 }} />
            ) : (
              <Text style={styles.statNumber}>{metrics.servicestoday}</Text>
            )}
            <Text style={styles.statLabel}>Servicios Hoy</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.statCard}
            activeOpacity={0.7}
            onPress={() => router.push('/clients')}
          >
            <View style={styles.statIcon}>
              <Users size={20} color={Colors.light.primary} />
            </View>
            {isLoading ? (
              <ActivityIndicator size="small" color={Colors.light.primary} style={{ marginVertical: 8 }} />
            ) : (
              <Text style={styles.statNumber}>{metrics.totalClients}</Text>
            )}
            <Text style={styles.statLabel}>Clientes</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.statCard}
            activeOpacity={0.7}
            onPress={() => setShowReportsModal(true)}
          >
            <View style={styles.statIcon}>
              <TrendingUp size={20} color={Colors.light.success} />
            </View>
            {isLoading ? (
              <ActivityIndicator size="small" color={Colors.light.primary} style={{ marginVertical: 8 }} />
            ) : (
              <Text style={styles.statNumber}>
                {metrics.averageSatisfaction > 0 
                  ? `${Math.round(metrics.averageSatisfaction * 20)}%` 
                  : '-'}
              </Text>
            )}
            <Text style={styles.statLabel}>Satisfacción</Text>
          </TouchableOpacity>
          
          <View style={styles.statCard}>
            <View style={styles.statIcon}>
              <Shield size={20} color={Colors.light.secondary} />
            </View>
            <Text style={styles.statNumber}>100%</Text>
            <Text style={styles.statLabel}>Privacidad</Text>
          </View>
        </View>
      </View>

      <View style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>Características Destacadas</Text>
        
        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Zap size={24} color={Colors.light.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Análisis IA Profesional</Text>
            <Text style={styles.featureDescription}>
              Diagnóstico capilar avanzado con reconocimiento de imagen y recomendaciones personalizadas
            </Text>
          </View>
        </View>
        
        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Shield size={24} color={Colors.light.success} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Privacidad Total</Text>
            <Text style={styles.featureDescription}>
              Política "Analizar y Descartar" - Las imágenes se procesan y eliminan inmediatamente
            </Text>
          </View>
        </View>
        
        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Users size={24} color={Colors.light.secondary} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Historial Inteligente</Text>
            <Text style={styles.featureDescription}>
              Seguimiento completo de clientes con alertas de alergias y recomendaciones personalizadas
            </Text>
          </View>
        </View>
      </View>
      
      {/* Reports Modal */}
      <Modal
        visible={showReportsModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowReportsModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <View style={{ width: 24 }} />
          </View>
          
          <InventoryReports onProductSelect={handleProductSelect} />
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    padding: spacing.lg,
    paddingTop: 40,
    backgroundColor: "#FFFFFF",
  },
  title: {
    fontSize: typography.sizes['4xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  quickActions: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  primaryAction: {
    backgroundColor: Colors.light.primary,
    borderRadius: radius.lg,
    padding: spacing.lg,
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: radius.full,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: "white",
    marginBottom: spacing.xs,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 20,
  },
  secondaryActions: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  secondaryAction: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "white",
    padding: spacing.md,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.sm,
  },
  statsSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: spacing.sm,
  },
  statCard: {
    flex: 1,
    minWidth: "45%",
    alignItems: "center",
    padding: spacing.md,
    backgroundColor: "white",
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: "#F5F5F7",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  statNumber: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  featuresSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  featureCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
    padding: spacing.md,
    backgroundColor: "white",
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: "#F5F5F7",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
});