import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Switch, ScrollView, TextInput, Modal, FlatList, Alert } from "react-native";
import { ChevronRight, ChevronDown, ChevronUp, Plus, X, User, Bell, Palette, LogOut, MapPin, Globe, Ruler, Briefcase, Clock, Check, Search, Calculator, BarChart3, Package, Droplets, Shield, Settings as SettingsIcon, DollarSign, AlertTriangle, Users } from "lucide-react-native";
import { router } from 'expo-router';
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useAuthStore } from "@/stores/auth-store";
import { useAIAnalysisStore } from "@/stores/ai-analysis-store";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { professionalHairColorBrands, Brand, ProductLine, searchBrands, getAllCountries } from "@/constants/reference-data/brands-data";
import { countriesData, getCountryByCode } from "@/constants/reference-data/countries-data";
import { CountryCode, RegionalConfig } from "@/types/regional";
import { currenciesData, getCurrencyByCode } from "@/constants/reference-data/currencies-data";
import { SearchableList } from "@/components/SearchableList";
import PricingSettingsModal from "@/components/settings/PricingSettingsModal";
import { useTeamStore } from "@/stores/team-store";
import { usePermissions } from "@/hooks/usePermissions";
import { useScrollToTopOnFocus } from "@/hooks/useScrollToTopOnFocus";

export default function SettingsScreen() {
  const { user, signOut, preferredBrandLines, addBrandLineSelection, removeBrandLineSelection } = useAuthStore();
  const { settings: aiSettings, updateSettings: updateAISettings, clearAnalysisHistory } = useAIAnalysisStore();
  const { configuration, regionalConfig, updateInventoryControlLevel, formatCurrency, updateCountry, updateMeasurementSystem, updateLanguage, updatePricing, skipSafetyVerification, setSkipSafetyVerification, setHasCompletedOnboarding } = useSalonConfigStore();
  const { members, getMembersBySalon } = useTeamStore();
  const { hasPermission, isOwner, can } = usePermissions();
  
  // Get team members for current salon
  const teamMembers = user?.salonId ? getMembersBySalon(user.salonId) : [];
  
  // Business Information
  const [businessName, setBusinessName] = useState("Mi Salón de Belleza");
  const [streetAddress, setStreetAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [country, setCountry] = useState(configuration.countryCode || "ES");
  
  // Professional Settings
  const [licenseNumber, setLicenseNumber] = useState("");
  const [yearsExperience, setYearsExperience] = useState("5");
  const [specializations, setSpecializations] = useState([
    "Coloración",
    "Mechas",
    "Tratamientos"
  ]);
  
  // System Settings
  const [language, setLanguage] = useState(configuration.language || "es");
  const [measurementSystem, setMeasurementSystem] = useState(configuration.measurementSystem || "metric");
  
  // Brand/Line Selection
  const [expandedBrands, setExpandedBrands] = useState<string[]>([]);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [selectedBrandForModal, setSelectedBrandForModal] = useState<Brand | null>(null);
  const [tempSelectedLines, setTempSelectedLines] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredBrands, setFilteredBrands] = useState<Brand[]>(professionalHairColorBrands);
  const [selectedCountryFilter, setSelectedCountryFilter] = useState<string>("");
  
  // Inventory Control Level Modal
  const [showInventoryModal, setShowInventoryModal] = useState(false);
  const [tempInventoryLevel, setTempInventoryLevel] = useState(configuration.inventoryControlLevel);
  
  // Pricing Settings Modal
  const [showPricingModal, setShowPricingModal] = useState(false);
  
  
  // Country Selection Modal
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showMeasurementModal, setShowMeasurementModal] = useState(false);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  
  // Other states
  const [newSpecialization, setNewSpecialization] = useState("");
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // ScrollView ref and hook for scroll to top on focus
  const scrollRef = useRef<ScrollView>(null);
  useScrollToTopOnFocus(scrollRef);

  // Search and filter effect
  useEffect(() => {
    let results = searchBrands(searchQuery);
    
    if (selectedCountryFilter) {
      results = results.filter(brand => brand.country === selectedCountryFilter);
    }
    
    setFilteredBrands(results);
  }, [searchQuery, selectedCountryFilter]);

  const addSpecialization = () => {
    if (newSpecialization.trim() && !specializations.includes(newSpecialization.trim())) {
      setSpecializations([...specializations, newSpecialization.trim()]);
      setNewSpecialization("");
    }
  };

  const removeSpecialization = (spec: string) => {
    setSpecializations(specializations.filter(s => s !== spec));
  };

  const toggleBrandExpansion = (brandId: string) => {
    setExpandedBrands(prev => 
      prev.includes(brandId) 
        ? prev.filter(id => id !== brandId)
        : [...prev, brandId]
    );
  };

  const openBrandModal = (brand?: Brand) => {
    if (brand) {
      setSelectedBrandForModal(brand);
      const existingSelection = preferredBrandLines.find(bl => bl.brandId === brand.id);
      setTempSelectedLines(existingSelection ? existingSelection.selectedLines : []);
    } else {
      setSelectedBrandForModal(null);
      setTempSelectedLines([]);
    }
    setSearchQuery("");
    setSelectedCountryFilter("");
    setFilteredBrands(professionalHairColorBrands);
    setShowBrandModal(true);
  };

  const toggleLineSelection = (lineId: string) => {
    setTempSelectedLines(prev => 
      prev.includes(lineId)
        ? prev.filter(id => id !== lineId)
        : [...prev, lineId]
    );
  };

  const saveBrandLineSelection = async () => {
    if (selectedBrandForModal) {
      if (tempSelectedLines.length > 0) {
        await addBrandLineSelection(selectedBrandForModal.id, tempSelectedLines);
      } else {
        await removeBrandLineSelection(selectedBrandForModal.id);
      }
    }
    setShowBrandModal(false);
    setSelectedBrandForModal(null);
    setTempSelectedLines([]);
  };

  const getSelectedBrandLines = () => {
    const result: Array<{ brand: Brand; selectedLines: ProductLine[] }> = [];
    
    preferredBrandLines.forEach(bl => {
      const brand = professionalHairColorBrands.find(b => b.id === bl.brandId);
      if (brand) {
        const selectedLines = brand.lines.filter(line => bl.selectedLines.includes(line.id));
        result.push({
          brand,
          selectedLines
        });
      }
    });
    
    return result;
  };

  const removeBrandSelection = async (brandId: string) => {
    await removeBrandLineSelection(brandId);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSelectedCountryFilter("");
  };

  const openInventoryModal = () => {
    setTempInventoryLevel(configuration.inventoryControlLevel);
    setShowInventoryModal(true);
  };

  const saveInventoryLevel = () => {
    updateInventoryControlLevel(tempInventoryLevel);
    setShowInventoryModal(false);
  };

  const getInventoryLevelDisplay = () => {
    switch (configuration.inventoryControlLevel) {
      case "solo-formulas":
        return "Solo Fórmulas";
      case "smart-cost":
        return "Smart Cost";
      case "control-total":
        return "Control Total";
      default:
        return "Smart Cost";
    }
  };

  const inventoryLevels = [
    {
      id: "solo-formulas",
      title: "Solo Fórmulas",
      subtitle: "Coloración rápida y simple",
      badge: "Ideal para empezar",
      badgeColor: Colors.light.accent,
      description: "Genera fórmulas profesionales de coloración sin gestionar inventario de tintes ni costos.",
      icon: Palette,
      features: [
        "Fórmulas de coloración personalizadas",
        "Conversión entre marcas de tintes",
        "Historial de servicios de color",
        "Análisis capilar con IA"
      ]
    },
    {
      id: "smart-cost",
      title: "Smart Cost",
      subtitle: "Conoce la rentabilidad de tus colores",
      badge: "Recomendado",
      badgeColor: Colors.light.primary,
      description: "Calcula automáticamente el costo de cada servicio de coloración y tu margen de ganancia por cliente.",
      icon: Calculator,
      features: [
        "Todo lo anterior +",
        "Costo por servicio de coloración",
        "Margen de ganancia por cliente",
        "Reportes de rentabilidad por color",
        "Análisis de consumo de tintes"
      ]
    },
    {
      id: "control-total",
      title: "Control Total",
      subtitle: "Gestión completa de coloración",
      badge: "Para salones profesionales",
      badgeColor: Colors.light.secondary,
      description: "Control total del inventario de productos de coloración con movimientos, alertas y consumo automático de tintes.",
      icon: BarChart3,
      features: [
        "Todo lo anterior +",
        "Inventario completo de tintes y oxidantes",
        "Movimientos de stock de coloración",
        "Alertas de reposición de productos",
        "Consumo automático por fórmula",
        "Control de caducidad de tintes"
      ]
    }
  ];

  const countries = getAllCountries();
  
  // Get current country info
  const currentCountryInfo = getCountryByCode(country as CountryCode);


  return (
    <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.profileSection}>
        <View style={styles.profileInfo}>
          <View style={styles.profileAvatar}>
            <Text style={styles.profileInitial}>{user?.name?.charAt(0) || "E"}</Text>
          </View>
          <View style={styles.profileDetails}>
            <Text style={styles.profileName}>{user?.name || "Estilista"}</Text>
            <Text style={styles.profileEmail}>{user?.email || "<EMAIL>"}</Text>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Editar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Business Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <MapPin size={20} color={Colors.light.primary} />
          <Text style={styles.sectionTitle}>Información del Negocio</Text>
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Nombre del Negocio</Text>
          <TextInput
            style={styles.textInput}
            value={businessName}
            onChangeText={setBusinessName}
            placeholder="Nombre de tu salón"
            placeholderTextColor={Colors.light.gray}
          />
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Dirección</Text>
          <TextInput
            style={styles.textInput}
            value={streetAddress}
            onChangeText={setStreetAddress}
            placeholder="Calle y número"
            placeholderTextColor={Colors.light.gray}
          />
        </View>
        <View style={styles.inputRow}>
          <View style={styles.inputHalf}>
            <Text style={styles.inputLabel}>Ciudad</Text>
            <TextInput
              style={styles.textInput}
              value={city}
              onChangeText={setCity}
              placeholder="Ciudad"
              placeholderTextColor={Colors.light.gray}
            />
          </View>
          <View style={styles.inputHalf}>
            <Text style={styles.inputLabel}>Código Postal</Text>
            <TextInput
              style={styles.textInput}
              value={postalCode}
              onChangeText={setPostalCode}
              placeholder="CP"
              placeholderTextColor={Colors.light.gray}
            />
          </View>
        </View>
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => setShowCountryModal(true)}
        >
          <Text style={styles.settingLabel}>País</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {currentCountryInfo?.flag} {currentCountryInfo?.localName || country}
            </Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Professional Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Briefcase size={20} color={Colors.light.secondary} />
          <Text style={styles.sectionTitle}>Mi Licencia Profesional</Text>
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Número de Licencia</Text>
          <TextInput
            style={styles.textInput}
            value={licenseNumber}
            onChangeText={setLicenseNumber}
            placeholder="Número de licencia profesional"
            placeholderTextColor={Colors.light.gray}
          />
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Años de Experiencia</Text>
          <TextInput
            style={styles.textInput}
            value={yearsExperience}
            onChangeText={setYearsExperience}
            placeholder="Años de experiencia"
            placeholderTextColor={Colors.light.gray}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Especializaciones</Text>
          <View style={styles.tagsContainer}>
            {specializations.map((spec, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{spec}</Text>
                <TouchableOpacity onPress={() => removeSpecialization(spec)}>
                  <X size={14} color={Colors.light.gray} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
          <View style={styles.addItemContainer}>
            <TextInput
              style={styles.addItemInput}
              placeholder="Añadir especialización..."
              placeholderTextColor={Colors.light.gray}
              value={newSpecialization}
              onChangeText={setNewSpecialization}
            />
            <TouchableOpacity style={styles.addItemButton} onPress={addSpecialization}>
              <Plus size={20} color={Colors.light.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* My Team - Solo visible para owners */}
      {isOwner && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Users size={20} color={Colors.light.accent} />
            <Text style={styles.sectionTitle}>Mi Equipo</Text>
          </View>
          
          <TouchableOpacity 
            style={styles.settingItem}
            onPress={() => router.push('/settings/team')}
          >
            <View style={styles.teamInfo}>
              <Text style={styles.settingLabel}>Gestionar Equipo</Text>
              <Text style={styles.teamDescription}>
                {teamMembers.length === 0 
                  ? 'Añade empleados a tu salón'
                  : `${teamMembers.filter(m => m.status === 'active').length} empleados activos`}
              </Text>
            </View>
            <View style={styles.settingValue}>
              <ChevronRight size={16} color={Colors.light.gray} />
            </View>
          </TouchableOpacity>
        </View>
      )}

      {/* Safety Configuration */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Shield size={20} color={Colors.light.warning} />
          <Text style={styles.sectionTitle}>Configuración de Seguridad</Text>
        </View>
        
        <View style={styles.switchItem}>
          <View style={styles.notificationInfo}>
            <Text style={styles.settingLabel}>Saltar Verificación de Seguridad</Text>
            <Text style={styles.notificationDescription}>
              {skipSafetyVerification 
                ? 'La verificación de seguridad está desactivada. Los servicios iniciarán directamente.'
                : 'Se requiere completar el checklist de seguridad antes de cada servicio.'}
            </Text>
            {skipSafetyVerification && (
              <View style={styles.warningNote}>
                <AlertTriangle size={14} color={Colors.light.warning} />
                <Text style={styles.warningNoteText}>
                  Al desactivar esta verificación, asumes toda la responsabilidad legal sobre las medidas de seguridad.
                </Text>
              </View>
            )}
          </View>
          <Switch
            trackColor={{ false: Colors.light.lightGray, true: Colors.light.warning }}
            thumbColor={"white"}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={(value) => {
              if (value) {
                Alert.alert(
                  "⚠️ Advertencia de Seguridad",
                  "Al desactivar la verificación de seguridad, asumes toda la responsabilidad legal sobre el cumplimiento de los protocolos de seguridad en tu salón.\n\n¿Estás seguro de que deseas continuar?",
                  [
                    { text: "Cancelar", style: "cancel" },
                    { 
                      text: "Sí, desactivar", 
                      style: "destructive",
                      onPress: () => setSkipSafetyVerification(true)
                    }
                  ]
                );
              } else {
                setSkipSafetyVerification(false);
              }
            }}
            value={skipSafetyVerification}
          />
        </View>
      </View>

      {/* System Configuration */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Globe size={20} color={Colors.light.accent} />
          <Text style={styles.sectionTitle}>Configuración Regional</Text>
        </View>
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => setShowLanguageModal(true)}
        >
          <Text style={styles.settingLabel}>Idioma</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {language === 'es' ? 'Español' : 
               language === 'en' ? 'English' :
               language === 'pt' ? 'Português' :
               language === 'fr' ? 'Français' : language}
            </Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => setShowMeasurementModal(true)}
        >
          <Text style={styles.settingLabel}>Sistema de Medidas</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>
              {measurementSystem === 'metric' ? 'Métrico (ml, g)' : 'Imperial (fl oz, oz)'}
            </Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => setShowCurrencyModal(true)}
        >
          <Text style={styles.settingLabel}>Moneda</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>{configuration.pricing.currency} ({configuration.pricing.currencySymbol})</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingItem} onPress={openInventoryModal}>
          <Text style={styles.settingLabel}>Nivel de Control de Coloración</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>{getInventoryLevelDisplay()}</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Inventory & Pricing Configuration */}
      {configuration.inventoryControlLevel !== 'solo-formulas' && can.modifyPrices && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <DollarSign size={20} color={Colors.light.primary} />
            <Text style={styles.sectionTitle}>Configuración de Inventario y Precios</Text>
          </View>
          
          <TouchableOpacity style={styles.settingItem} onPress={() => setShowPricingModal(true)}>
            <Text style={styles.settingLabel}>Configuración de Precios</Text>
            <View style={styles.settingValue}>
              <View style={styles.settingValueBadge}>
                <Text style={styles.settingValueBadgeText}>Margen {configuration.pricing.defaultMarkupPercentage}%</Text>
              </View>
              <ChevronRight size={16} color={Colors.light.gray} style={{ marginLeft: 8 }} />
            </View>
          </TouchableOpacity>
          
          <View style={styles.pricingInfo}>
            <View style={styles.pricingInfoItem}>
              <Text style={styles.pricingInfoLabel}>Política de Redondeo</Text>
              <Text style={styles.pricingInfoValue}>
                {configuration.pricing.roundingPolicy === 'none' ? 'Sin redondeo' :
                 configuration.pricing.roundingPolicy === 'nearest' ? 'Más cercano' :
                 configuration.pricing.roundingPolicy === 'up' ? 'Hacia arriba' : 'Hacia abajo'}
              </Text>
            </View>
            <View style={styles.pricingInfoItem}>
              <Text style={styles.pricingInfoLabel}>Precio Mínimo</Text>
              <Text style={styles.pricingInfoValue}>{formatCurrency(configuration.pricing.minimumServicePrice)}</Text>
            </View>
            {configuration.pricing.includeTaxInPrice && (
              <View style={styles.pricingInfoItem}>
                <Text style={styles.pricingInfoLabel}>Impuestos Incluidos</Text>
                <Text style={styles.pricingInfoValue}>{configuration.pricing.taxPercentage}%</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Brand and Line Preferences */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Droplets size={20} color={Colors.light.secondary} />
          <Text style={styles.sectionTitle}>Marcas de Coloración Preferidas</Text>
        </View>
        
        {/* Selected Brands Summary */}
        {getSelectedBrandLines().length > 0 && (
          <View style={styles.selectedBrandsContainer}>
            {getSelectedBrandLines().map((item, index) => (
              <View key={index} style={styles.selectedBrandCard}>
                <View style={styles.selectedBrandHeader}>
                  <View style={styles.selectedBrandInfo}>
                    <Text style={styles.selectedBrandName}>{item.brand.name}</Text>
                    <Text style={styles.selectedBrandCountry}>{item.brand.country}</Text>
                  </View>
                  <TouchableOpacity onPress={() => removeBrandSelection(item.brand.id)}>
                    <X size={16} color={Colors.light.gray} />
                  </TouchableOpacity>
                </View>
                <View style={styles.selectedLinesContainer}>
                  {item.selectedLines.map((line, lineIndex) => (
                    <View key={lineIndex} style={styles.selectedLineTag}>
                      <Text style={styles.selectedLineText}>{line.name}</Text>
                    </View>
                  ))}
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Add Brand Button */}
        <TouchableOpacity 
          style={styles.addBrandButton}
          onPress={() => openBrandModal()}
        >
          <Plus size={20} color={Colors.light.primary} />
          <Text style={styles.addBrandButtonText}>Añadir Marca de Coloración</Text>
        </TouchableOpacity>
      </View>


      {/* Notifications */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Bell size={20} color={Colors.light.warning} />
          <Text style={styles.sectionTitle}>Notificaciones</Text>
        </View>
        <View style={styles.switchItem}>
          <View style={styles.notificationInfo}>
            <Text style={styles.settingLabel}>Recordatorios de Servicios</Text>
            <Text style={styles.notificationDescription}>Alerta cuando tengas servicios pendientes de completar</Text>
          </View>
          <Switch
            trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
            thumbColor={"white"}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={setNotificationsEnabled}
            value={notificationsEnabled}
          />
        </View>
        {configuration.inventoryControlLevel !== 'solo-formulas' && (
          <View style={styles.switchItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.settingLabel}>Alertas de Inventario</Text>
              <Text style={styles.notificationDescription}>Aviso cuando un producto esté por agotarse</Text>
            </View>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
              thumbColor={"white"}
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={(value) => console.log('Inventory alerts:', value)}
              value={false}
            />
          </View>
        )}
        <View style={styles.switchItem}>
          <View style={styles.notificationInfo}>
            <Text style={styles.settingLabel}>Tips y Mejores Prácticas</Text>
            <Text style={styles.notificationDescription}>Consejos para aprovechar al máximo Salonier</Text>
          </View>
          <Switch
            trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
            thumbColor={"white"}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={(value) => console.log('Tips enabled:', value)}
            value={true}
          />
        </View>
      </View>

      {/* Data & Backup */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Package size={20} color={Colors.light.text} />
          <Text style={styles.sectionTitle}>Datos y Respaldo</Text>
        </View>
        
        {/* Privacy Mode Switch */}
        <View style={styles.switchItem}>
          <View style={styles.notificationInfo}>
            <Text style={styles.settingLabel}>Modo Privacidad</Text>
            <Text style={styles.notificationDescription}>
              {aiSettings.privacyMode 
                ? 'Máxima protección - Sin guardar fotos ni historiales de análisis'
                : 'Historial completo - Ideal para seguimiento de servicios'}
            </Text>
          </View>
          <Switch
            trackColor={{ false: Colors.light.lightGray, true: Colors.light.success }}
            thumbColor={"white"}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={(value) => updateAISettings({ 
              privacyMode: value,
              autoFaceBlur: value ? true : aiSettings.autoFaceBlur,
              saveAnalysisHistory: value ? false : aiSettings.saveAnalysisHistory
            })}
            value={aiSettings.privacyMode}
          />
        </View>
        
        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingLabel}>Exportar mis Datos</Text>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>CSV / PDF</Text>
            <ChevronRight size={16} color={Colors.light.gray} />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingLabel}>Política de Privacidad</Text>
          <ChevronRight size={16} color={Colors.light.gray} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingLabel}>Términos de Uso</Text>
          <ChevronRight size={16} color={Colors.light.gray} />
        </TouchableOpacity>
        
        {/* Clear History Button - Only show if privacy mode is off */}
        {!aiSettings.privacyMode && (
          <TouchableOpacity 
            style={styles.dangerButton}
            onPress={() => clearAnalysisHistory()}
          >
            <Text style={styles.dangerButtonText}>Limpiar Historial de Análisis</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity 
          style={styles.dangerButton}
          onPress={() => Alert.alert(
            'Eliminar Todos mis Datos',
            '¿Estás seguro? Esta acción no se puede deshacer.',
            [
              { text: 'Cancelar', style: 'cancel' },
              { text: 'Eliminar', style: 'destructive', onPress: () => console.log('Delete all data') }
            ]
          )}
        >
          <Text style={styles.dangerButtonText}>Eliminar Todos mis Datos</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <TouchableOpacity style={styles.logoutButton} onPress={signOut}>
          <LogOut size={20} color="white" style={styles.logoutIcon} />
          <Text style={styles.logoutButtonText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.versionInfo}>
        <Text style={styles.versionText}>Salonier v1.0.0</Text>
      </View>

      {/* Development Tools - Only visible in development mode */}
      {__DEV__ && (
        <View style={[styles.section, styles.developmentSection]}>
          <View style={styles.sectionHeader}>
            <SettingsIcon size={20} color="#856404" />
            <Text style={[styles.sectionTitle, styles.developmentSectionTitle]}>Desarrollo (Temporal)</Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.settingItem, styles.developmentSettingItem]}
            onPress={() => {
              setHasCompletedOnboarding(false);
              router.push('/onboarding/welcome');
            }}
          >
            <Text style={[styles.settingLabel, styles.developmentSettingLabel]}>Resetear y Probar Onboarding</Text>
            <ChevronRight size={16} color="#856404" />
          </TouchableOpacity>
          
          <Text style={styles.developmentWarning}>
            ⚠️ Esta sección es temporal para pruebas. Solo visible en modo desarrollo.
          </Text>
        </View>
      )}


      {/* Inventory Control Level Modal */}
      <Modal
        visible={showInventoryModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowInventoryModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Nivel de Control de Coloración</Text>
            <TouchableOpacity onPress={saveInventoryLevel}>
              <Text style={styles.modalSaveText}>Guardar</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <View style={styles.inventoryModalHeader}>
              <Text style={styles.inventoryModalDescription}>
                Elige cómo prefieres gestionar tus productos de coloración. Puedes cambiarlo cuando quieras.
              </Text>
            </View>

            <View style={styles.inventoryLevelsContainer}>
              {inventoryLevels.map((level) => {
                const IconComponent = level.icon;
                const isSelected = tempInventoryLevel === level.id;
                
                return (
                  <TouchableOpacity
                    key={level.id}
                    style={[
                      styles.inventoryLevelCard,
                      isSelected && styles.inventoryLevelCardSelected
                    ]}
                    onPress={() => setTempInventoryLevel(level.id as "solo-formulas" | "smart-cost" | "control-total")}
                  >
                    <View style={styles.inventoryLevelHeader}>
                      <View style={styles.inventoryLevelIconContainer}>
                        <IconComponent size={24} color={Colors.light.primary} />
                      </View>
                      <View style={styles.inventoryLevelTitleContainer}>
                        <View style={styles.inventoryLevelTitleRow}>
                          <Text style={styles.inventoryLevelTitle}>{level.title}</Text>
                          <View style={[styles.inventoryLevelBadge, { backgroundColor: level.badgeColor + "15" }]}>
                            <Text style={[styles.inventoryLevelBadgeText, { color: level.badgeColor }]}>
                              {level.badge}
                            </Text>
                          </View>
                        </View>
                        <Text style={styles.inventoryLevelSubtitle}>{level.subtitle}</Text>
                      </View>
                      {isSelected && (
                        <View style={styles.selectedIndicator}>
                          <Check size={20} color={Colors.light.primary} />
                        </View>
                      )}
                    </View>
                    
                    <Text style={styles.inventoryLevelDescription}>{level.description}</Text>
                    
                    <View style={styles.inventoryLevelFeatures}>
                      {level.features.map((feature, index) => (
                        <View key={index} style={styles.inventoryLevelFeature}>
                          <Check size={16} color={Colors.light.accent} />
                          <Text style={styles.inventoryLevelFeatureText}>{feature}</Text>
                        </View>
                      ))}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>

            <View style={styles.inventoryModalFooter}>
              <View style={styles.inventoryModalNote}>
                <Text style={styles.inventoryModalNoteText}>
                  💡 Puedes cambiar tu nivel de control en cualquier momento desde Ajustes. Tus datos de coloración se conservarán al cambiar de nivel.
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Brand Selection Modal */}
      <Modal
        visible={showBrandModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowBrandModal(false)}>
              <Text style={styles.modalCancelText}>Cancelar</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {selectedBrandForModal ? selectedBrandForModal.name : "Seleccionar Marca"}
            </Text>
            {selectedBrandForModal && (
              <TouchableOpacity onPress={saveBrandLineSelection}>
                <Text style={styles.modalSaveText}>Guardar</Text>
              </TouchableOpacity>
            )}
            {!selectedBrandForModal && (
              <View style={styles.modalSaveText} />
            )}
          </View>
          
          {selectedBrandForModal ? (
            <View style={styles.modalContent}>
              <View style={styles.brandDetailHeader}>
                <Text style={styles.brandDetailName}>{selectedBrandForModal.name}</Text>
                <Text style={styles.brandDetailCountry}>{selectedBrandForModal.country}</Text>
                {selectedBrandForModal.description && (
                  <Text style={styles.brandDetailDescription}>{selectedBrandForModal.description}</Text>
                )}
              </View>
              
              <Text style={styles.linesTitle}>Selecciona las líneas de coloración que utilizas:</Text>
              
              {selectedBrandForModal.lines.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.lineItem,
                    tempSelectedLines.includes(item.id) && styles.lineItemSelected
                  ]}
                  onPress={() => toggleLineSelection(item.id)}
                >
                  <View style={styles.lineItemContent}>
                    <Text style={[
                      styles.lineItemName,
                      tempSelectedLines.includes(item.id) && styles.lineItemNameSelected
                    ]}>
                      {item.name}
                    </Text>
                    {item.description && (
                      <Text style={styles.lineItemDescription}>{item.description}</Text>
                    )}
                  </View>
                  {tempSelectedLines.includes(item.id) && (
                    <Check size={20} color={Colors.light.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.modalContent}>
              {/* Search and Filter Section */}
              <View style={styles.searchSection}>
                <View style={styles.searchContainer}>
                  <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Buscar marcas de coloración..."
                    placeholderTextColor={Colors.light.gray}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                  />
                  {searchQuery.length > 0 && (
                    <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                      <X size={16} color={Colors.light.gray} />
                    </TouchableOpacity>
                  )}
                </View>
                
                {/* Country Filter */}
                <ScrollView 
                  horizontal 
                  showsHorizontalScrollIndicator={false}
                  style={styles.countryFilterContainer}
                  contentContainerStyle={styles.countryFilterContent}
                >
                  <TouchableOpacity
                    style={[
                      styles.countryFilterChip,
                      !selectedCountryFilter && styles.countryFilterChipSelected
                    ]}
                    onPress={() => setSelectedCountryFilter("")}
                  >
                    <Text style={[
                      styles.countryFilterText,
                      !selectedCountryFilter && styles.countryFilterTextSelected
                    ]}>
                      Todos
                    </Text>
                  </TouchableOpacity>
                  {countries.map((countryName) => (
                    <TouchableOpacity
                      key={countryName}
                      style={[
                        styles.countryFilterChip,
                        selectedCountryFilter === countryName && styles.countryFilterChipSelected
                      ]}
                      onPress={() => setSelectedCountryFilter(
                        selectedCountryFilter === countryName ? "" : countryName
                      )}
                    >
                      <Text style={[
                        styles.countryFilterText,
                        selectedCountryFilter === countryName && styles.countryFilterTextSelected
                      ]}>
                        {countryName}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Results Summary */}
              <View style={styles.resultsHeader}>
                <Text style={styles.resultsText}>
                  {filteredBrands.length} marca{filteredBrands.length !== 1 ? 's' : ''} de coloración encontrada{filteredBrands.length !== 1 ? 's' : ''}
                </Text>
              </View>

              {/* Brands List */}
              {filteredBrands.length === 0 ? (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>
                    No se encontraron marcas de coloración que coincidan con tu búsqueda
                  </Text>
                </View>
              ) : (
                filteredBrands.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    style={styles.brandItem}
                    onPress={() => openBrandModal(item)}
                  >
                    <View style={styles.brandItemContent}>
                      <Text style={styles.brandItemName}>{item.name}</Text>
                      <Text style={styles.brandItemCountry}>{item.country}</Text>
                      <Text style={styles.brandItemLines}>{item.lines.length} líneas de coloración disponibles</Text>
                      {item.description && (
                        <Text style={styles.brandItemDescription}>{item.description}</Text>
                      )}
                    </View>
                    <ChevronRight size={20} color={Colors.light.gray} />
                  </TouchableOpacity>
                ))
              )}
            </View>
          )}
        </View>
      </Modal>

      {/* Pricing Settings Modal */}
      <PricingSettingsModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
      />
      
      {/* Country Selection Modal */}
      <Modal
        visible={showCountryModal}
        animationType="slide"
        presentationStyle="formSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCountryModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Seleccionar País</Text>
            <TouchableOpacity 
              onPress={() => {
                const selectedCountry = countriesData.find(c => c.code === country);
                if (selectedCountry) {
                  updateCountry(country as CountryCode);
                  setLanguage(selectedCountry.config.language);
                  setMeasurementSystem(selectedCountry.config.measurementSystem);
                }
                setShowCountryModal(false);
              }}
            >
              <Text style={styles.modalSaveText}>Guardar</Text>
            </TouchableOpacity>
          </View>
          
          <SearchableList
            data={countriesData}
            searchKeys={['name', 'localName', 'code']}
            placeholder="Buscar país..."
            selectedValue={country}
            getItemValue={(item) => item.code}
            onSelect={(item) => setCountry(item.code)}
            groupBy={(item) => item.config.region}
            keyExtractor={(item) => item.code}
            renderItem={(item, isSelected) => (
              <View style={[
                styles.countryItem,
                isSelected && styles.countryItemSelected
              ]}>
                <View style={styles.countryItemContent}>
                  <Text style={styles.countryFlag}>{item.flag}</Text>
                  <View style={styles.countryInfo}>
                    <Text style={[
                      styles.countryName,
                      isSelected && styles.countryNameSelected
                    ]}>
                      {item.localName}
                    </Text>
                    <Text style={styles.countryDetails}>
                      {item.config.currency} • {item.config.measurementSystem === 'metric' ? 'Métrico' : 'Imperial'}
                    </Text>
                  </View>
                </View>
                {isSelected && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </View>
            )}
          />
        </View>
      </Modal>
      
      {/* Measurement System Modal */}
      <Modal
        visible={showMeasurementModal}
        animationType="slide"
        presentationStyle="formSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowMeasurementModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Sistema de Medidas</Text>
            <TouchableOpacity 
              onPress={() => {
                updateMeasurementSystem(measurementSystem as 'metric' | 'imperial');
                setShowMeasurementModal(false);
              }}
            >
              <Text style={styles.modalSaveText}>Guardar</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <View style={styles.measurementOptionsContainer}>
              <TouchableOpacity
              style={[
                styles.measurementOption,
                measurementSystem === 'metric' && styles.measurementOptionSelected
              ]}
              onPress={() => setMeasurementSystem('metric')}
            >
              <View style={styles.measurementOptionContent}>
                <Text style={[
                  styles.measurementOptionTitle,
                  measurementSystem === 'metric' && styles.measurementOptionTitleSelected
                ]}>
                  Sistema Métrico
                </Text>
                <Text style={styles.measurementOptionDescription}>
                  Mililitros (ml) y gramos (g)
                </Text>
                <Text style={styles.measurementOptionExample}>
                  Ejemplo: 60ml de tinte, 90ml de oxidante
                </Text>
              </View>
              {measurementSystem === 'metric' && (
                <Check size={20} color={Colors.light.primary} />
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.measurementOption,
                measurementSystem === 'imperial' && styles.measurementOptionSelected
              ]}
              onPress={() => setMeasurementSystem('imperial')}
            >
              <View style={styles.measurementOptionContent}>
                <Text style={[
                  styles.measurementOptionTitle,
                  measurementSystem === 'imperial' && styles.measurementOptionTitleSelected
                ]}>
                  Sistema Imperial
                </Text>
                <Text style={styles.measurementOptionDescription}>
                  Onzas líquidas (fl oz) y onzas (oz)
                </Text>
                <Text style={styles.measurementOptionExample}>
                  Ejemplo: 2 fl oz de color, 3 fl oz de developer
                </Text>
              </View>
              {measurementSystem === 'imperial' && (
                <Check size={20} color={Colors.light.primary} />
              )}
            </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Modal>
      
      {/* Currency Modal */}
      <Modal
        visible={showCurrencyModal}
        animationType="slide"
        presentationStyle="formSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCurrencyModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Seleccionar Moneda</Text>
            <TouchableOpacity 
              onPress={() => {
                const currency = getCurrencyByCode(configuration.pricing.currency);
                if (currency) {
                  updatePricing({
                    currency: currency.code,
                    currencySymbol: currency.symbol,
                  });
                }
                setShowCurrencyModal(false);
              }}
            >
              <Text style={styles.modalSaveText}>Guardar</Text>
            </TouchableOpacity>
          </View>
          
          <SearchableList
            data={currenciesData}
            searchKeys={['code', 'name']}
            placeholder="Buscar moneda..."
            selectedValue={configuration.pricing.currency}
            getItemValue={(item) => item.code}
            onSelect={(item) => {
              updatePricing({
                currency: item.code,
                currencySymbol: item.symbol,
              });
            }}
            keyExtractor={(item) => item.code}
            renderItem={(item, isSelected) => (
              <View style={[
                styles.currencyItem,
                isSelected && styles.currencyItemSelected
              ]}>
                <View style={styles.currencyItemContent}>
                  <View style={styles.currencySymbolContainer}>
                    <Text style={styles.currencySymbol}>{item.symbol}</Text>
                  </View>
                  <View style={styles.currencyInfo}>
                    <Text style={[
                      styles.currencyCode,
                      isSelected && styles.currencyCodeSelected
                    ]}>
                      {item.code}
                    </Text>
                    <Text style={styles.currencyName}>
                      {item.name}
                    </Text>
                  </View>
                </View>
                {isSelected && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </View>
            )}
            ListHeaderComponent={
              <View style={styles.currencyHeaderNote}>
                <Text style={styles.currencyHeaderNoteText}>
                  💡 La moneda sugerida para {currentCountryInfo?.localName} es {currentCountryInfo?.config.currency}
                </Text>
              </View>
            }
          />
        </View>
      </Modal>
      
      {/* Language Modal */}
      <Modal
        visible={showLanguageModal}
        animationType="slide"
        presentationStyle="formSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowLanguageModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Idioma</Text>
            <TouchableOpacity 
              onPress={() => {
                updateLanguage(language);
                setShowLanguageModal(false);
              }}
            >
              <Text style={styles.modalSaveText}>Guardar</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {[
              { code: 'es', name: 'Español', flag: '🇪🇸' },
              { code: 'en', name: 'English', flag: '🇬🇧' },
              { code: 'pt', name: 'Português', flag: '🇧🇷' },
              { code: 'fr', name: 'Français', flag: '🇫🇷' },
            ].map(lang => (
              <TouchableOpacity
                key={lang.code}
                style={[
                  styles.languageOption,
                  language === lang.code && styles.languageOptionSelected
                ]}
                onPress={() => setLanguage(lang.code)}
              >
                <View style={styles.languageOptionContent}>
                  <Text style={styles.languageFlag}>{lang.flag}</Text>
                  <Text style={[
                    styles.languageName,
                    language === lang.code && styles.languageNameSelected
                  ]}>
                    {lang.name}
                  </Text>
                </View>
                {language === lang.code && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  profileSection: {
    backgroundColor: "#FFFFFF",
    margin: spacing.lg,
    borderRadius: radius.lg,
    padding: spacing.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  profileInitial: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  profileEmail: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  editButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    backgroundColor: "#F5F5F7",
  },
  editButtonText: {
    color: Colors.light.primary,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
  },
  section: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: radius.lg,
    padding: spacing.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  settingLabel: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    fontWeight: typography.weights.medium,
    flex: 1,
    marginRight: 16,
  },
  settingValue: {
    flexDirection: "row",
    alignItems: "center",
    flexShrink: 0,
  },
  settingValueText: {
    fontSize: 16,
    color: Colors.light.gray,
    marginRight: 8,
    maxWidth: 200,
    textAlign: "right",
  },
  settingValueBadge: {
    backgroundColor: Colors.light.primary + "15",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  settingValueBadgeText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  inputGroup: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  textInput: {
    height: 48,
    backgroundColor: "#F5F5F7",
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    borderWidth: 0,
  },
  inputRow: {
    flexDirection: "row",
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  inputHalf: {
    flex: 1,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  tag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F7",
    borderRadius: radius.full,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  tagText: {
    fontSize: 14,
    color: Colors.light.text,
    marginRight: 8,
    fontWeight: "500",
  },
  addItemContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  addItemInput: {
    flex: 1,
    height: 48,
    backgroundColor: "#F5F5F7",
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: Colors.light.text,
  },
  addItemButton: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: "#F5F5F7",
    justifyContent: "center",
    alignItems: "center",
  },
  selectedBrandsContainer: {
    marginBottom: 16,
  },
  selectedBrandCard: {
    backgroundColor: "#F5F5F7",
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 0,
  },
  selectedBrandHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  selectedBrandInfo: {
    flex: 1,
  },
  selectedBrandName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 2,
  },
  selectedBrandCountry: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  selectedLinesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  selectedLineTag: {
    backgroundColor: "white",
    borderRadius: radius.md,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  selectedLineText: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: "500",
  },
  addBrandButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "white",
    borderRadius: radius.md,
    paddingVertical: spacing.md,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: "dashed",
  },
  addBrandButtonText: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: "600",
    marginLeft: 8,
  },
  switchItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  dangerButton: {
    backgroundColor: Colors.light.error + "15",
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: "center",
    marginTop: 8,
  },
  dangerButtonText: {
    color: Colors.light.error,
    fontWeight: "600",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.secondary,
    borderRadius: radius.md,
    paddingVertical: spacing.md,
    ...shadows.md,
  },
  logoutIcon: {
    marginRight: spacing.sm,
  },
  logoutButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.bold,
    fontSize: typography.sizes.base,
  },
  versionInfo: {
    alignItems: "center",
    padding: spacing.lg,
  },
  versionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalCancelText: {
    fontSize: 16,
    color: Colors.light.gray,
    width: 80,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    textAlign: "center",
    flex: 1,
  },
  modalSaveText: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: "600",
    width: 80,
    textAlign: "right",
  },
  modalContent: {
    flex: 1,
  },
  inventoryModalHeader: {
    padding: 20,
    paddingBottom: 0,
  },
  inventoryModalDescription: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: "center",
    lineHeight: 22,
  },
  inventoryLevelsContainer: {
    padding: 20,
    gap: 16,
  },
  inventoryLevelCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.lg,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  inventoryLevelCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.surface,
  },
  inventoryLevelHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  inventoryLevelIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  inventoryLevelTitleContainer: {
    flex: 1,
  },
  inventoryLevelTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  inventoryLevelTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
    marginRight: 12,
  },
  inventoryLevelBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  inventoryLevelBadgeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  inventoryLevelSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    fontWeight: "500",
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
  },
  inventoryLevelDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
    marginBottom: 16,
  },
  inventoryLevelFeatures: {
    gap: 8,
  },
  inventoryLevelFeature: {
    flexDirection: "row",
    alignItems: "center",
  },
  inventoryLevelFeatureText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 8,
    fontWeight: "500",
  },
  inventoryModalFooter: {
    padding: 20,
  },
  inventoryModalNote: {
    backgroundColor: Colors.light.accent + "10",
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.accent,
  },
  inventoryModalNoteText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  searchSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.light.text,
  },
  clearButton: {
    padding: 4,
  },
  countryFilterContainer: {
    maxHeight: 50,
  },
  countryFilterContent: {
    paddingRight: 20,
  },
  countryFilterChip: {
    backgroundColor: Colors.light.surface,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  countryFilterChipSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  countryFilterText: {
    fontSize: 14,
    color: Colors.light.text,
    fontWeight: "500",
  },
  countryFilterTextSelected: {
    color: "white",
    fontWeight: "600",
  },
  resultsHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: Colors.light.surface,
  },
  resultsText: {
    fontSize: 14,
    color: Colors.light.gray,
    fontWeight: "500",
  },
  brandDetailHeader: {
    marginBottom: 24,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  brandDetailName: {
    fontSize: 24,
    fontWeight: "700",
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandDetailCountry: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  brandDetailDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  linesTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  brandItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  brandItemContent: {
    flex: 1,
  },
  brandItemName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandItemCountry: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 2,
  },
  brandItemLines: {
    fontSize: 12,
    color: Colors.light.accent,
    marginBottom: 4,
  },
  brandItemDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    lineHeight: 16,
  },
  lineItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  lineItemSelected: {
    backgroundColor: Colors.light.primary + "10",
  },
  lineItemContent: {
    flex: 1,
  },
  lineItemName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.light.text,
    marginBottom: 4,
  },
  lineItemNameSelected: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  lineItemDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 18,
  },
  emptyState: {
    padding: 40,
    alignItems: "center",
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: "center",
    lineHeight: 24,
  },
  pricingInfo: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
  },
  pricingInfoItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  pricingInfoLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  pricingInfoValue: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  // Regional config styles
  regionSection: {
    marginBottom: 24,
  },
  regionTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.light.gray,
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  countryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  countryItemSelected: {
    backgroundColor: Colors.light.primary + "10",
  },
  countryItemContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  countryFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  countryInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.light.text,
    marginBottom: 2,
  },
  countryNameSelected: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  countryDetails: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  measurementOption: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 20,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  measurementOptionSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + "05",
  },
  measurementOptionContent: {
    flex: 1,
  },
  measurementOptionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 4,
  },
  measurementOptionTitleSelected: {
    color: Colors.light.primary,
  },
  measurementOptionDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  measurementOptionExample: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: "italic",
  },
  languageOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  languageOptionSelected: {
    backgroundColor: Colors.light.primary + "10",
  },
  languageOptionContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.light.text,
  },
  languageNameSelected: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  // Measurement modal styles
  measurementOptionsContainer: {
    paddingVertical: 20,
  },
  // Currency modal styles
  currencyItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  currencyItemSelected: {
    backgroundColor: Colors.light.primary + "10",
  },
  currencyItemContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  currencySymbolContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.primary,
  },
  currencyInfo: {
    flex: 1,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 2,
  },
  currencyCodeSelected: {
    color: Colors.light.primary,
  },
  currencyName: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  currencyHeaderNote: {
    backgroundColor: Colors.light.accent + "15",
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.accent,
  },
  currencyHeaderNoteText: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  notificationInfo: {
    flex: 1,
    marginRight: 12,
  },
  notificationDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
    lineHeight: 16,
  },
  warningNote: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: Colors.light.warning + "15",
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
    gap: 6,
  },
  warningNoteText: {
    flex: 1,
    fontSize: 11,
    color: Colors.light.warning,
    lineHeight: 16,
  },
  // Development section styles
  developmentSection: {
    backgroundColor: '#FFF3CD',
    marginTop: 20,
  },
  developmentSectionTitle: {
    color: '#856404',
  },
  developmentSettingItem: {
    backgroundColor: '#FFF8DC',
  },
  developmentSettingLabel: {
    color: '#856404',
  },
  developmentWarning: {
    fontSize: 12,
    color: '#856404',
    marginTop: 8,
    paddingHorizontal: 16,
  },
  // Team section styles
  teamInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  teamDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  teamMembersPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  teamMemberAvatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  teamMemberInitial: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.primary,
  },
});