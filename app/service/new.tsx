import React, { useState, useEffect } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import Colors from "@/constants/colors";
import { useClientStore } from "@/stores/client-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import { useAIAnalysisStore } from "@/stores/ai-analysis-store";
import { useDashboardStore } from "@/stores/dashboard-store";
import Toast from "@/components/Toast";

// Import our new components and hooks
import { useServiceFlow, STEPS } from "@/src/service/hooks/useServiceFlow";
import { useServicePersistence } from "@/src/service/hooks/useServicePersistence";
import { ServiceHeader } from "@/src/service/components/ServiceHeader";
import { StepIndicator } from "@/src/service/components/StepIndicator";
import { DiagnosisStep } from "@/src/service/components/DiagnosisStep";
import { DesiredColorStep } from "@/src/service/components/DesiredColorStep";
import { FormulationStep } from "@/src/service/components/FormulationStep";
import { CompletionStep } from "@/src/service/components/CompletionStep";

export default function NewServiceScreen() {
  const { clientId, restoreDraft } = useLocalSearchParams();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // Use our new hooks
  const {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep
  } = useServiceFlow();

  const { saveServiceDraft, loadServiceDraft, deleteServiceDraft } = useServicePersistence();

  // Client Store
  const { getClient } = useClientStore();

  // Client History Store
  const { saveCompletedService } = useClientHistoryStore();

  // AI Analysis Store (for passing to components)
  const { analysisResult } = useAIAnalysisStore();
  
  // Dashboard Store (for refreshing metrics)
  const { refreshMetrics } = useDashboardStore();

  // Load client data on mount
  useEffect(() => {
    const loadClientData = async () => {
      if (clientId && typeof clientId === 'string') {
        try {
          const clientData = await getClient(clientId);
          if (clientData) {
            updateServiceData({
              client: clientData,
              clientId: clientId
            });

            // Try to load draft if requested
            if (restoreDraft === 'true') {
              const draft = loadServiceDraft(clientId);
              if (draft) {
                // Restore service data from draft - only restore compatible data
                const restoredData: any = {};

                if (draft.diagnosisData) {
                  Object.assign(restoredData, draft.diagnosisData);
                }
                if (draft.desiredData) {
                  Object.assign(restoredData, draft.desiredData);
                }
                if (draft.formulationData) {
                  Object.assign(restoredData, draft.formulationData);
                }
                if (draft.resultData) {
                  Object.assign(restoredData, draft.resultData);
                }

                updateServiceData(restoredData);
                showToastMessage("Borrador restaurado correctamente");
              }
            }
          }
        } catch (error) {
          console.error('Error loading client:', error);
          showToastMessage("Error al cargar datos del cliente");
        }
      }
    };

    loadClientData();
  }, [clientId, restoreDraft]);

  // Auto-save draft periodically
  useEffect(() => {
    if (serviceData.clientId && serviceData.client) {
      const autoSaveInterval = setInterval(() => {
        saveServiceDraft(serviceData, currentStep);
      }, 30000); // Auto-save every 30 seconds

      return () => clearInterval(autoSaveInterval);
    }
  }, [serviceData, currentStep]);

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const handleSaveDraft = (silent: boolean = false) => {
    if (serviceData.clientId) {
      saveServiceDraft(serviceData, currentStep);
      if (!silent) {
        showToastMessage("Borrador guardado");
      }
    }
  };

  const handleFinishService = async () => {
    try {
      // Add service to client history
      if (serviceData.clientId && serviceData.client) {
        // Save the completed service to database
        await saveCompletedService({
          clientId: serviceData.clientId,
          clientName: serviceData.client.name,
          serviceType: 'color_service',
          formula: serviceData.formula,
          formulaData: serviceData.formulaData,
          technique: serviceData.applicationTechnique,
          processingTime: serviceData.processingTime,
          developerVolume: serviceData.developerVolume,
          satisfaction: serviceData.clientSatisfaction,
          notes: serviceData.resultNotes,
          beforePhotos: serviceData.diagnosisImage ? [serviceData.diagnosisImage] : [],
          afterPhotos: serviceData.resultImage ? [serviceData.resultImage] : [],
          aiAnalysis: analysisResult || null,
        });

        // Delete draft after successful completion
        deleteServiceDraft(serviceData.clientId);
        
        // Small delay to ensure Zustand persists the deletion to AsyncStorage
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Refresh dashboard metrics
        await refreshMetrics();

        showToastMessage("Servicio completado y guardado");

        // Navigate to dashboard after a short delay
        setTimeout(() => {
          router.replace('/(tabs)');
        }, 1500);
      }
    } catch (error) {
      console.error('Error finishing service:', error);
      Alert.alert("Error", "No se pudo completar el servicio. Por favor intenta nuevamente.");
    }
  };

  const handleStepNavigation = (stepIndex: number) => {
    if (canNavigateToStep(stepIndex)) {
      goToStep(stepIndex);
    } else {
      Alert.alert(
        "Paso no disponible",
        "Completa los pasos anteriores antes de continuar.",
        [{ text: "OK" }]
      );
    }
  };

  // Silent save for AI analysis results
  const handleSaveDraftSilent = () => {
    handleSaveDraft(true);
  };

  // Render the appropriate step component
  const renderCurrentStep = () => {
    const commonProps = {
      data: serviceData,
      onUpdate: updateServiceData,
      onNext: goToNextStep,
      onBack: goToPreviousStep,
      onSave: handleSaveDraft,
      onSaveSilent: handleSaveDraftSilent
    };

    switch (currentStep) {
      case 0:
        return <DiagnosisStep {...commonProps} />;
      case 1:
        return <DesiredColorStep {...commonProps} />;
      case 2:
        return (
          <FormulationStep
            {...commonProps}
            analysisResult={analysisResult}
          />
        );
      case 3:
        return (
          <CompletionStep
            {...commonProps}
            onNext={handleFinishService}
          />
        );
      default:
        return <DiagnosisStep {...commonProps} />;
    }
  };

  return (
    <View style={styles.container}>
      <ServiceHeader
        currentStep={currentStep}
        clientName={serviceData.client?.name}
        onSaveDraft={serviceData.clientId ? handleSaveDraft : undefined}
        onBack={goToPreviousStep}
      />

      <StepIndicator
        steps={STEPS}
        currentStep={currentStep}
        onStepPress={handleStepNavigation}
        canNavigateToStep={(stepIndex: number) => Boolean(canNavigateToStep(stepIndex))}
      />

      {renderCurrentStep()}

      {showToast && (
        <Toast
          message={toastMessage}
          onHide={() => setShowToast(false)}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
});