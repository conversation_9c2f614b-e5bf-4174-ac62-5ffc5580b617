# Rork Salonier Copilot - Documentación de Trabajo

## 🎯 Estado Actual del Proyecto

**Última actualización**: 2025-01-11

**✅ ESTADO**: Aplicación completamente migrada y optimizada en Supabase. Sistema cloud con performance mejorado y seguridad reforzada.

**Resumen ejecutivo**: Aplicación de asistente de coloración capilar con IA implementada en React Native/Expo. Sistema completo de gestión de salón con análisis inteligente de color, generación de fórmulas personalizadas y control de inventario.

**Estado del sistema (OPTIMIZADO)**:
- ✅ Base de datos PostgreSQL con índices optimizados
- ✅ Autenticación con protección de contraseñas comprometidas (pendiente activar)
- ✅ RLS policies optimizadas para mejor performance
- ✅ Edge Functions actualizadas con modelos GPT-4o
- ✅ Sistema offline-first con sincronización automática
- ✅ Seguridad reforzada en todas las funciones SQL

**Características principales implementadas**:
- ✅ Sistema multi-usuario con permisos granulares (7 niveles)
- ✅ Wizard de seguridad de 4 pasos antes de cada servicio
- ✅ Análisis de color con IA y generación de fórmulas automática
- ✅ Gestión de inventario con 3 niveles de control
- ✅ Soporte completo para configuración regional (ES/EN, ml/oz, €/$)
- ✅ Captura de fotos guiada para Android
- ✅ Sistema de autocompletado de alergias con 23+ opciones
- ✅ Diseño minimalista consistente (#FFFFFF base)
- ✅ Sistema offline-first con sincronización automática
- ✅ Indicadores visuales de estado de conexión
- 🆕 Performance optimizado con políticas RLS mejoradas
- 🆕 Seguridad reforzada en funciones SQL
- 🆕 Edge Functions con retry logic y rate limiting

---

## 📋 Cambios Recientes

### 2025-01-11 - OPTIMIZACIÓN COMPLETA DE SUPABASE
- **Performance RLS**: 9 políticas optimizadas con `(SELECT auth.uid())`
- **Seguridad SQL**: 4 funciones aseguradas con `SET search_path`
- **Índices optimizados**: 5 creados, 11 eliminados por no uso
- **Edge Function actualizada**: GPT-4o con retry logic
- **Constraints de validación**: Stock positivo, email válido, etc.
- **Triggers automáticos**: Campo updated_at siempre actualizado
- **Documentación completa**: Scripts de validación y guías
- **Fix client_consents**: Agregadas columnas faltantes (consent_data, safety_checklist, etc.)
- **Mejora manejo de errores IA**: Logs detallados y mensajes de error descriptivos
- **Implementación base64 para imágenes**: Máxima privacidad, sin URLs públicas
- **Optimización de tamaños**: Reducción agresiva para evitar stack overflow (400px → 300px)
- 🆕 **Generación de fórmulas regional**: Sistema consciente de unidades, idioma y regulaciones por país

### 2025-07-11 - MIGRACIÓN A SUPABASE (COMPLETADA)
- **Integración Supabase completa**: Base de datos PostgreSQL, Auth, Storage, Edge Functions
- **Patrón UI Optimistic**: TODOS los stores migrados para offline-first
- **Cola de sincronización**: Sistema robusto para operaciones offline funcionando
- **Indicadores visuales**: SyncIndicator muestra estado de conexión
- **Edge Functions**: Análisis IA funcionando en servidor
- **Sistema de registro robusto**: Polling + fallback manual implementado
- **RLS policies corregidas**: Eliminada recursión infinita
- **Sincronización completa**: Todos los stores sincronizan correctamente

### 2025-07-10 - Sistema Multi-Usuario y Mejoras
- **Login diferenciado por rol**: Propietarios vs empleados
- **Acceso mejorado a reportes**: Nueva ruta dedicada `/app/inventory/reports.tsx`
- **Sistema de permisos completo**: 7 permisos granulares
- **Autocompletado de alergias**: 23 alergias comunes categorizadas
- **Configuración regional completa**: Hook `useRegionalUnits` centralizado

### 2025-07-09 - Diseño y UX
- **Diseño minimalista global**: Tema blanco consistente
- **Edición de clientes**: CRUD completo con validación
- **Unificación de interfaces**: Eliminados formularios duplicados
- **Formulario cliente mejorado**: Campos de seguridad críticos

*Para historial completo ver: `CHANGELOG.md`*

---

## 🔧 Arquitectura y Componentes Clave

### Sistema de Seguridad
**Propósito**: Garantizar la seguridad del cliente antes de cualquier servicio químico

**Componentes principales**:
- `app/service/safety-verification.tsx` - Wizard de 4 pasos
- `types/safety.ts` - Tipos y estructuras de datos
- `stores/client-store.ts` - Almacenamiento de consentimientos

**Flujo**:
1. Checklist de seguridad básica
2. Test de parche (48h antes)
3. Verificaciones críticas (sales metálicas, henna, formol)
4. Consentimiento informado con firma digital

**Configuración**: Toggle en settings para saltar verificación (con advertencia legal)

### Sistema de Inventario
**Propósito**: Control completo del stock y costes del salón

**Componentes principales**:
- `app/(tabs)/inventory.tsx` - Vista principal con cards
- `stores/inventory-store.ts` - Estado global del inventario
- `hooks/useRegionalUnits.ts` - Gestión de unidades regionales
- `components/FormulaCostBreakdown.tsx` - Cálculo de costes

**Niveles de control**:
1. **Solo Fórmulas**: Genera fórmulas con costes estimados
2. **Smart Cost**: Calcula costes reales del inventario
3. **Control Total**: Incluye verificación y consumo de stock

**Características**:
- Stock en unidades base (ml/g)
- Conversión automática de unidades regionales
- Indicadores visuales de stock bajo
- Reportes detallados accesibles

### Sistema Multi-Usuario
**Propósito**: Permitir trabajo en equipo con control de acceso

**Componentes principales**:
- `types/permissions.ts` - Definición de permisos
- `hooks/usePermissions.ts` - Hook para verificación
- `stores/auth-store.ts` - Estado de autenticación
- `stores/team-store.ts` - Gestión de empleados

**Permisos disponibles**:
- `VIEW_ALL_CLIENTS` - Ver todos los clientes
- `VIEW_COSTS` - Ver costes y rentabilidad
- `MODIFY_PRICES` - Modificar precios
- `MANAGE_INVENTORY` - Gestionar inventario
- `VIEW_REPORTS` - Ver reportes
- `CREATE_USERS` - Crear empleados
- `DELETE_DATA` - Eliminar datos

**Seguridad**: Contraseñas hasheadas con SHA256, reseteo seguro por propietario

### Sistema de Formulación
**Propósito**: Generar fórmulas profesionales de coloración

**Componentes principales**:
- `app/service/new.tsx` - Flujo principal del servicio
- `components/ai/ColorAnalysis.tsx` - Análisis con IA
- `components/ProportionCalculator.tsx` - Cálculo de proporciones
- `utils/color-formulation.ts` - Lógica de formulación

**Características**:
- Análisis de fotos con IA
- Cálculo automático de proporciones
- Soporte para múltiples técnicas
- Adaptación regional de terminología

---

## 🐛 Problemas Conocidos y Soluciones

### Problemas Resueltos en Migración a Supabase (2025-07-11)

#### "No salon ID found" ✅
**Problema**: Los stores intentaban sincronizar antes de autenticación
**Solución**: Eliminado auto-sync, sincronización solo post-login

#### "Invalid API key" ✅
**Problema**: Credenciales de Supabase no configuradas
**Solución**: Archivo `.env.local` actualizado con credenciales reales

#### Recursión infinita en RLS ✅
**Problema**: Políticas RLS mal configuradas causaban bucle infinito
**Solución**: Políticas simplificadas y migración 010 aplicada

#### Error de registro persistente ✅
**Problema**: Trigger SQL no siempre creaba perfil/salón
**Solución**: Implementado polling + fallback manual en auth-store

#### Payload de IA incorrecto ✅
**Problema**: Edge Function esperaba 'payload' pero recibía 'data'
**Solución**: Actualizado cliente y llamadas para usar estructura correcta

### UI/UX

#### expo-camera y CameraView (2025-07-05)
**Problema**: `The <CameraView> component does not support children`
```jsx
// ✅ SOLUCIÓN
<View style={styles.container}>
  <CameraView style={styles.camera} />
  <View style={StyleSheet.absoluteFillObject}>
    {/* overlay content */}
  </View>
</View>
```

#### Duplicación de tabs en diagnóstico (2025-07-08)
**Problema**: Tabs duplicados en la interfaz
**Solución**: Eliminar código duplicado en líneas 1880-1931 de `app/service/new.tsx`

#### Navegación con diseño minimalista (2025-07-09)
**Problema**: Botones de regreso invisibles
**Solución**: BaseHeader actualizado con botón gris claro y texto "Atrás"

### Lógica de Negocio

#### Coherencia de niveles de inventario (2025-07-08)
**Problema**: Verificación de stock en niveles incorrectos
**Solución**: Stock verification solo en "Control Total"

#### Visualización de costes (2025-07-08)
**Problema**: Costes no aparecían en "Solo Fórmulas"
**Solución**: Todos los niveles muestran costes (estimados o reales)

#### Sistema de unidades coherente (2025-07-10)
**Problema**: Mezcla de unidades base y envases
**Solución**: Stock siempre en unidades base (ml/g), UI muestra ambos

### Integración y Datos

#### Wizard no aprovechaba datos del cliente (2025-07-10)
**Problema**: Preguntas repetitivas en wizard de seguridad
**Solución**: Pre-llenado automático con `detectClientRisks()`

#### Configuración regional incompleta (2025-07-10)
**Problema**: € hardcodeado, unidades mezcladas
**Solución**: Hook `useRegionalUnits` centralizado

---

## 📚 Referencias Rápidas

### Documentos principales
- `CHANGELOG.md` - Historial detallado de todas las versiones
- `todo.md` - Lista de tareas pendientes y roadmap
- `README.md` - Documentación general del proyecto
- `README_SETUP.md` - Guía de configuración para Supabase
- `IMPLEMENTATION_SUMMARY.md` - Resumen completo de la migración Supabase

### Carpetas clave
- `/app` - Pantallas y navegación (Expo Router)
- `/components` - Componentes reutilizables
- `/stores` - Estados globales (Zustand)
- `/types` - Definiciones TypeScript
- `/utils` - Funciones de utilidad
- `/hooks` - React hooks personalizados
- `/constants` - Constantes y configuración
- `/lib` - Configuración de servicios externos (Supabase)
- `/supabase` - Migraciones y configuración de base de datos

### APIs y servicios
- OpenAI API - Análisis de imágenes y generación de fórmulas
- Supabase - Base de datos, autenticación, storage, edge functions
- Expo Camera - Captura de fotos
- AsyncStorage - Persistencia local (solo datos no sincronizados)
- Zustand - Gestión de estado

---

## 📝 Notas de Desarrollo

### Comandos útiles
```bash
# Desarrollo
npm run ios
npm run android
npm run web

# Testing
npm test
npm run test:watch

# Build
eas build --platform ios
eas build --platform android
```

### Consideraciones importantes
- La app usa Expo SDK 51
- Requiere permisos de cámara
- ✅ **MIGRACIÓN COMPLETA**: Los datos se sincronizan con Supabase
- ✅ **CONFIGURACIÓN**: Credenciales y migraciones SQL aplicadas
- Cada salón tiene su propia instancia con aislamiento de datos (RLS)
- Sistema offline-first: funciona sin conexión y sincroniza automáticamente

---

## Apéndice: Flujo de Trabajo Estándar para Claude Code

### 1. Fase de Análisis
Primero, analiza exhaustivamente el problema:
- Lee TODOS los archivos relevantes del código base antes de hacer cambios
- Comprende la estructura del proyecto y las dependencias
- Identifica impactos potenciales y efectos secundarios
- Documenta tu comprensión de la implementación actual

### 2. Fase de Planificación
Crea un plan detallado con:
- **Lista de tareas**: Divide el trabajo en tareas atómicas e independientes (máx. 5-10 líneas por tarea)
- **Dependencias**: Nota qué tareas dependen de otras
- **Archivos a modificar**: Lista archivos exactos con breve descripción de cambios
- **Nuevos archivos/funciones**: Especifica convenciones de nombres y propósito
- **Estrategia de pruebas**: ¿Cómo verificarás cada cambio?

#### Plantilla del Plan:
```
## Plan de Implementación
### Tareas:
- [ ] Tarea 1: [Descripción] (Archivo: xyz.js)
- [ ] Tarea 2: [Descripción] (Archivos: abc.py, def.py)

### Dependencias:
- La Tarea 2 depende de la Tarea 1

### Evaluación de Riesgos:
- Posibles cambios disruptivos en...
- Necesidad de mantener compatibilidad con...
```

### 3. Fase de Implementación
- **Un cambio a la vez**: Completa cada tarea antes de pasar a la siguiente
- **Prueba sobre la marcha**: Ejecuta pruebas relevantes después de cada cambio
- **Mantén cambios mínimos**: Prefiere múltiples commits pequeños sobre uno grande
- **Mantén consistencia**: Sigue el estilo y patrones de código existentes
- **Agrega comentarios**: Documenta lógica compleja o decisiones no obvias

### 4. Lineamientos de Calidad de Código
- **Simplicidad primero**: Elige la solución más simple que funcione
- **Sin optimización prematura**: Enfócate en la corrección antes que en el rendimiento
- **Principio DRY**: No te repitas, pero no sobre-abstraigas
- **Manejo de errores**: Siempre maneja casos límite y fallas potenciales
- **Seguridad de tipos**: Usa anotaciones de tipos donde sea aplicable

### 5. Requisitos de Documentación
Después de cada tarea, actualiza:
- `todo.md` con elementos completados marcados
- Comentarios en el código para lógica compleja
- README si la funcionalidad cambia
- Documentación de API si las interfaces cambian

### 6. Lista de Verificación de Revisión
Antes de considerar el trabajo completo:
- [ ] Todas las pruebas pasan
- [ ] Sin errores de linting
- [ ] El código sigue las convenciones del proyecto
- [ ] Los cambios son retrocompatibles (o los cambios disruptivos están documentados)
- [ ] Impacto en el rendimiento considerado
- [ ] Implicaciones de seguridad revisadas
- [ ] Documentación actualizada

### 7. Resumen Final
Agrega a `todo.md`:
```markdown
## Sección de Revisión - [Fecha]
### Cambios Realizados:
- [Componente/Archivo]: [Qué cambió y por qué]
- [Componente/Archivo]: [Qué cambió y por qué]

### Pruebas Realizadas:
- [Tipo de prueba]: [Resultado]

### Problemas Conocidos/Trabajo Futuro:
- [Problema]: [Descripción y solución potencial]

### Cambios Disruptivos:
- [Si hay]: [Guía de migración]
```

### 8. Mejores Prácticas para Claude Code
- **Usa rutas explícitas**: Siempre especifica rutas completas desde la raíz del proyecto
- **Guardados incrementales**: Guarda archivos frecuentemente para evitar perder trabajo
- **Nombres de variables claros**: Prefiere nombres descriptivos sobre comentarios
- **Diseño modular**: Mantén funciones pequeñas y enfocadas
- **Mentalidad de control de versiones**: Piensa en términos de commits atómicos
- **Pide aclaraciones**: Cuando los requisitos sean ambiguos, pregunta antes de implementar

### 9. Errores Comunes a Evitar
- No modifiques múltiples sistemas no relacionados en una tarea
- No asumas contenido de archivos - siempre lee primero
- No ignores mensajes de error - abórdalos inmediatamente
- No omitas pruebas en cambios "simples"
- No dejes comentarios TODO sin rastrearlos

### 10. Procedimientos de Emergencia
Si algo se rompe:
1. Detente y evalúa el daño
2. Revierte el último cambio si es necesario
3. Documenta qué salió mal
4. Crea un plan de corrección antes de proceder
5. Prueba la corrección exhaustivamente

### 11. Comandos Útiles Frecuentes
```bash
# Ver estructura del proyecto
find . -type f -name "*.py" | head -20

# Buscar en archivos
grep -r "función_específica" --include="*.js"

# Verificar sintaxis Python
python -m py_compile archivo.py

# Ejecutar pruebas específicas
pytest tests/test_modulo.py::test_funcion
```

### 12. Flujo de Comunicación
- **Reporta progreso**: Actualiza después de cada tarea completada
- **Comunica bloqueos**: Si algo te detiene, repórtalo inmediatamente
- **Sugiere mejoras**: Si ves oportunidades de refactorización, documéntalas
- **Confirma entendimiento**: Resume requisitos complejos antes de implementar
- **Comportamiento**: Evita simplemente estar de acuerdo con mis puntos o aceptar mis conclusiones sin cuestionarlas. Quiero un desafío intelectual real, no solo afirmación. Siempre que proponga una idea, haz esto:
  • Cuestiona mis suposiciones. ¿Qué estoy tratando como cierto que podría ser cuestionable?
  • Ofrece un punto de vista escéptico. ¿Qué objeciones plantearía una voz crítica y bien informada?
  • Revisa mi razonamiento. ¿Hay fallos o saltos en la lógica que haya pasado por alto?
  • Sugiere ángulos alternativos. ¿De qué otra forma podría interpretarse, verse o desafiarse la idea?
  • Prioriza la precisión por encima del acuerdo. Si mi argumento es débil o incorrecto, corrígeme claramente y muéstrame cómo.
  • Sé constructivo pero riguroso. No estás aquí para discutir por discutir, sino para agudizar mi pensamiento y mantenerme honesto.

Si notas que caigo en sesgos o suposiciones infundadas, dilo claramente. Refinemos tanto nuestras conclusiones como la forma en la que llegamos a ellas.
Si necesitas contrastar tus ideas, busca en internet para afianzarlas y tener más contexto.

Cuando tengas duda sobre una tarea o lo que necesito, hazme preguntas aclaratorias hasta que estés 95% seguro de que puedes completar la tarea con éxito.
# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

      
      IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.