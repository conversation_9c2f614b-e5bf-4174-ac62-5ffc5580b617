# TODO List - Salonier Copilot

## Optimización Supabase - 2025-01-11

### Estado: COMPLETADO ✅

**Objetivo**: Optimizar la configuración de Supabase para mejorar performance, seguridad y coherencia con el proyecto.

### Tareas Críticas 🔴:
- [x] Sincronizar migraciones locales con Supabase
  - [x] Verificar y aplicar migración 009_performance_indexes.sql
  - [ ] Descargar migraciones 011 y 012 de Supabase al repo local
  - [x] Documentar estado actual de migraciones (ver SUPABASE_OPTIMIZATION_SUMMARY.md)
- [x] Optimizar políticas RLS (9 afectadas)
  - [x] Reemplazar `auth.uid()` con `(SELECT auth.uid())` en todas las políticas
- [x] Corregir funciones vulnerables (4 detectadas)
  - [x] Agregar `SECURITY DEFINER SET search_path = public` a funciones

### Tareas Importantes 🟡:
- [x] Crear índices faltantes en Foreign Keys (5 detectados)
  - [x] client_consents.service_id
  - [x] clients.created_by
  - [x] formulas.created_by
  - [x] salons.owner_id
  - [x] stock_movements.created_by
- [ ] Habilitar protección de contraseñas comprometidas (HaveIBeenPwned) - PENDIENTE en Dashboard
- [x] Actualizar Edge Function salonier-assistant
  - [x] Cambiar modelo de gpt-4-vision-preview a gpt-4o
  - [x] Mejorar manejo de rate limiting
  - [x] Actualizar cálculo de costos

### Tareas Recomendadas 🟢:
- [x] Eliminar índices no utilizados (11 detectados)
- [ ] Configurar pg_cron para limpieza automática de fotos temporales - PENDIENTE en Dashboard
- [ ] Agregar validación JSON Schema para salons.settings - FUTURA MEJORA
- [x] Crear constraints de validación
  - [x] CHECK stock_ml >= 0 en products
  - [x] Validar formato email en clients
  - [x] Validar service_date no futura
- [x] Implementar triggers para actualizar updated_at automáticamente

### Problemas Resueltos:
- ✅ **Performance**: RLS policies optimizadas con (SELECT auth.uid())
- ✅ **Seguridad**: Functions aseguradas con SET search_path
- ⚠️ **Coherencia**: Migraciones parcialmente sincronizadas (faltan 011 y 012)
- ✅ **Recursos**: 11 índices no utilizados eliminados

### Resumen de Optimizaciones:
- **3 migraciones aplicadas**: 009, 013, 014
- **9 políticas RLS optimizadas**
- **4 funciones aseguradas**
- **5 índices creados** en foreign keys
- **11 índices eliminados** por no uso
- **4 constraints agregados** para validación
- **5 triggers creados** para updated_at
- **1 Edge Function actualizada** con mejoras significativas

Ver detalles completos en: `SUPABASE_OPTIMIZATION_SUMMARY.md`

## Sección de Revisión - 2025-07-11

### Cambios Realizados:
- **Integración Supabase completa**: Base de datos, Auth, Storage, Edge Functions configurados
- **Stores migrados**: auth-store, sync-queue-store, client-store, inventory-store, client-history-store, ai-analysis-store
- **UI Optimistic pattern**: Implementado en todos los stores para operaciones offline-first
- **SyncIndicator**: Nuevo componente visual para mostrar estado de conexión
- **Credenciales configuradas**: Proyecto Supabase activo con credenciales reales
- **SUPABASE_PHILOSOPHY.md**: Documento de filosofía arquitectónica creado como "Constitución Técnica"
- **Flujo de registro mejorado**: Polling para esperar creación de perfil/salón + creación manual como fallback
- **Edge Function IA actualizada**: Corregida estructura de payload
- **Cliente para Edge Functions**: Creado lib/edge-functions.ts
- **Índices de rendimiento**: Migración SQL para mejorar performance
- **Stores migrados completamente**: team-store y salon-config-store ahora sincronizan con Supabase
- **Sincronización completa**: Todos los stores se sincronizan automáticamente después del login

### Pruebas Realizadas:
- **Creación de usuario**: Funciona correctamente en Supabase
- **Autenticación**: Login/logout funcionando
- **Sincronización**: Stores intentan sincronizar post-login

### Problemas Conocidos/Trabajo Futuro:
- **Edge Function IA**: Necesita configurar OPENAI_API_KEY en Supabase Dashboard
- **Migración de datos**: No hay proceso automático para datos existentes
- **Storage buckets**: Necesitan ser creados en Supabase
- **Admin API**: team-store usa admin API que requiere service_role key (no disponible en cliente)

### Problemas Resueltos:
- ✅ **Recursión infinita en RLS**: Migración aplicada exitosamente en Supabase
- ✅ **Políticas RLS corregidas**: Eliminada recursión, agregadas políticas INSERT
- ✅ **Trigger mejorado**: Agregado manejo de errores para no fallar el registro
- ✅ **Error de registro persistente**: Implementada función manual_user_setup como fallback robusto
- ✅ **RLS simplificado**: Políticas super simples para evitar bloqueos
- ✅ **Mejor logging**: Trigger con logging extensivo para debugging

### Cambios Disruptivos:
- **Autenticación**: Ahora usa Supabase Auth en lugar de AsyncStorage
- **Persistencia**: Los datos se sincronizan con la nube automáticamente
- **Configuración requerida**: Necesita credenciales de Supabase en .env.local
- **IA en Edge Function**: Las llamadas a OpenAI ahora van a través de Supabase

---

## Integración Backend Supabase - 2025-07-11

### Estado: COMPLETADO ✅

**Objetivo**: Migrar la aplicación a Supabase para permitir sincronización en la nube y trabajo colaborativo entre múltiples dispositivos.

### Tareas completadas:
- ✅ Configurar proyecto Supabase
- ✅ Crear esquema de base de datos (9 tablas)
- ✅ Implementar Row Level Security (RLS) - corregida recursión infinita
- ✅ Configurar triggers y funciones con fallback manual
- ✅ Desplegar Edge Function para IA
- ✅ Migrar auth-store a Supabase Auth con polling robusto
- ✅ Implementar sync-queue-store para offline-first
- ✅ Migrar client-store con UI optimistic updates
- ✅ Actualizar ai-analysis-store para usar Edge Function
- ✅ Reemplazar MockFormulationService con Edge Function en app/service/new.tsx
- ✅ Migrar inventory-store con UI optimistic pattern
- ✅ Migrar client-history-store con UI optimistic pattern
- ✅ Migrar team-store con sincronización completa
- ✅ Migrar salon-config-store con sincronización
- ✅ Añadir indicadores visuales de estado de sincronización (SyncIndicator)
- ✅ Integrar SyncIndicator en BaseHeader
- ✅ Verificar y mejorar pantallas de Login y Registro
- ✅ Mejorar manejo de errores de Supabase en auth
- ✅ Crear documentación SETUP_ENV.md
- ✅ Resolver error "No salon ID found" (auto-sync removido)
- ✅ Mejorar mensajes de error para configuración de Supabase
- ✅ Crear README_SETUP.md con guía completa de configuración
- ✅ Configurar clave API real de Supabase en .env.local
- ✅ Crear cliente para Edge Functions (lib/edge-functions.ts)
- ✅ Corregir payload de IA de 'data' a 'payload'
- ✅ Implementar fallback manual para creación de salón/perfil
- ✅ Aplicar todas las migraciones SQL (10 archivos)

### Tareas futuras (post-migración):
- [ ] Migración de datos existentes de AsyncStorage
- [ ] Configurar alertas de stock bajo en tiempo real con Supabase Realtime
- [ ] Implementar notificaciones push para recordatorios
- [ ] Crear Edge Function para gestión de equipo (evitar service_role en cliente)
- [ ] Dashboard web para propietarios
- [ ] Sistema de facturación integrado

### Dependencias añadidas:
- `@react-native-community/netinfo`: Para detección del estado de conexión (offline/online)
- `expo-image-manipulator`: Para comprimir imágenes antes de subirlas a Supabase
- `expo-file-system`: Para leer archivos de imágenes como base64

### Errores resueltos:
- **"No salon ID found"**: Eliminadas las llamadas de auto-sincronización al inicializar los stores. La sincronización ahora ocurre después del login exitoso a través del auth-store
- **"Invalid API key"**: Mejorados los mensajes de error para guiar al usuario a configurar Supabase correctamente. Creada documentación completa en README_SETUP.md
- **Configuración de Supabase**: Credenciales reales añadidas al archivo .env.local

### Problemas conocidos:
- **Tablas no existen**: Las migraciones SQL no se han ejecutado aún
- **Stores no migrados**: team-store, salon-config-store, settings-store
- **Edge Functions**: Creadas pero no desplegadas

### Arquitectura implementada:
- **Patrón UI Optimistic**: Actualizaciones instantáneas en UI, sincronización en background
- **Cola de sincronización**: Operaciones offline se guardan y sincronizan cuando hay conexión
- **Edge Functions**: Procesamiento de IA en servidor para proteger claves API
- **RLS Multi-tenant**: Aislamiento completo de datos entre salones

### Documentación actualizada:
- **CHANGELOG.md**: Versión 2.0.0-alpha.1 documentada como checkpoint
- **Claude.md**: Estado crítico documentado con detalles de migración
- **MIGRATION_STATUS.md**: Estado detallado de la migración creado
- **README_SETUP.md**: Guía completa de configuración de Supabase

## Sistema Multi-Usuario con Permisos - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Implementar un sistema de usuarios con permisos modulares para permitir que múltiples empleados usen la aplicación con diferentes niveles de acceso.

### Tareas completadas:
- ✅ Diseñar sistema de permisos modulares (7 permisos específicos)
- ✅ Actualizar auth-store con campos multi-usuario
- ✅ Crear team-store para gestión de empleados
- ✅ Implementar hook usePermissions
- ✅ Crear pantallas de gestión de equipo
- ✅ Actualizar login para empleados
- ✅ Migración automática para usuarios existentes
- ✅ Proteger vistas según permisos
- ✅ Instalar expo-crypto para hasheo seguro
- ✅ Implementar reseteo de contraseñas

### Permisos implementados:
1. **VIEW_ALL_CLIENTS**: Ver todos los clientes (vs solo los propios)
2. **VIEW_COSTS**: Ver costos y rentabilidad
3. **MODIFY_PRICES**: Modificar precios y márgenes
4. **MANAGE_INVENTORY**: Gestionar inventario (añadir/editar/eliminar)
5. **VIEW_REPORTS**: Ver reportes y estadísticas
6. **CREATE_USERS**: Crear nuevos usuarios/empleados
7. **DELETE_DATA**: Eliminar clientes y datos sensibles

### Protecciones aplicadas:
- **Inventario**: 
  - Botón "Nuevo" solo para MANAGE_INVENTORY
  - Precios solo visibles con VIEW_COSTS
  - Editar/Eliminar solo con MANAGE_INVENTORY
  - Reportes solo con VIEW_REPORTS
- **Clientes**:
  - Eliminar solo con DELETE_DATA
- **Settings**:
  - "Mi Equipo" solo para owners
  - Configuración de precios solo con MODIFY_PRICES

### Funcionalidades de gestión:
- **Crear empleados**: Con contraseña generada automáticamente
- **Editar permisos**: Actualización dinámica de permisos
- **Resetear contraseñas**: El propietario puede generar nuevas contraseñas temporales
- **Estado de empleados**: Activar/desactivar acceso

### Arquitectura:
- Sistema de 2 niveles: Owner (todos los permisos) + Empleados (permisos configurables)
- Migración automática para usuarios existentes
- Contraseñas hasheadas con SHA256 usando expo-crypto
- Persistencia con AsyncStorage
- Login unificado para propietarios y empleados

## Desglose de Costes Regional - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Mejorar el desglose de costes para mostrar unidades regionales correctamente.

### Cambios realizados:
- ✅ Actualizar FormulaCostBreakdown para usar useRegionalUnits
- ✅ Convertir ml a oz cuando sea necesario
- ✅ Mostrar símbolo de moneda correcto (€/$)
- ✅ Mantener precisión en cálculos
- ✅ Mejorar formato visual del desglose

### Componentes actualizados:
- `components/FormulaCostBreakdown.tsx`: Ahora muestra unidades correctas según región
- Hook `useRegionalUnits` utilizado para conversiones

## Configuración Regional Unificada - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Unificar toda la gestión de unidades regionales en un solo lugar.

### Tareas completadas:
- ✅ Crear hook useRegionalUnits centralizado
- ✅ Actualizar stores para usar el hook
- ✅ Eliminar lógica duplicada en componentes
- ✅ Actualizar inventory-store y todos los componentes relacionados

### Hook centralizado:
- `hooks/useRegionalUnits.ts`: Un solo lugar para toda la lógica de conversión
- Funciones: getVolumeUnit, getWeightUnit, getCurrency, convertVolume, convertWeight
- Componentes actualizados: +10 archivos migrados

## Sistema de Autocompletado de Alergias - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Implementar un sistema de autocompletado inteligente para el campo de alergias con las alergias más comunes.

### Funcionalidades implementadas:
- ✅ Lista curada de 23+ alergias comunes
- ✅ Categorización por tipo (Tintes, Químicos, Fragancias, Metales, Conservadores, Naturales)
- ✅ Autocompletado inteligente mientras el usuario escribe
- ✅ Soporte para múltiples alergias separadas por comas
- ✅ UI mejorada con dropdown de sugerencias
- ✅ Posibilidad de añadir alergias personalizadas

### Componentes actualizados:
- `app/client/new.tsx`: Campo de alergias con autocompletado
- `types/allergies.ts`: Nueva estructura de datos para alergias comunes

## Acceso Mejorado a Reportes - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Mejorar el acceso a los reportes de inventario con una nueva ruta dedicada.

### Cambios realizados:
- ✅ Crear nueva ruta `/app/inventory/reports.tsx`
- ✅ Mover lógica de generación de reportes
- ✅ Añadir botón de acceso directo en vista de inventario
- ✅ Mejorar diseño visual del reporte
- ✅ Proteger con permiso VIEW_REPORTS

### Mejoras adicionales:
- Diseño más limpio y profesional
- Navegación mejorada con BaseHeader
- Datos organizados en secciones claras

## Onboarding Diferenciado por Rol - 2025-07-10

### Estado: COMPLETADO ✅

**Objetivo**: Diferenciar el flujo de onboarding según el rol del usuario.

### Implementación:
- ✅ Solo propietarios pasan por onboarding completo
- ✅ Empleados van directo a la app
- ✅ Detección automática en login
- ✅ Actualización en registro

### Flujo actualizado:
1. **Propietarios nuevos**: Registro → Onboarding → App
2. **Empleados**: Login → App (sin onboarding)
3. **Propietarios existentes**: Login → App

## Mejoras de Rendimiento - 2025-07-08

### Estado: COMPLETADO ✅

**Objetivo**: Optimizar el rendimiento de la aplicación y mejorar la experiencia del usuario.

### Optimizaciones realizadas:
- ✅ Reducir re-renders innecesarios en listas
- ✅ Implementar React.memo en componentes pesados
- ✅ Optimizar cálculos de inventario
- ✅ Mejorar gestión de estado en formularios
- ✅ Lazy loading de componentes no críticos

### Resultados:
- Navegación más fluida
- Menor consumo de memoria
- Respuesta más rápida en interacciones

## Wizard de Seguridad Mejorado - 2025-07-09

### Estado: COMPLETADO ✅

**Objetivo**: Mejorar el wizard de seguridad para que sea más intuitivo y aproveche los datos existentes del cliente.

### Mejoras implementadas:
- ✅ Pre-llenado automático de campos desde el perfil del cliente
- ✅ Detección inteligente de riesgos con `detectClientRisks()`
- ✅ Saltar pasos innecesarios si no hay riesgos
- ✅ UI más clara con indicadores visuales
- ✅ Opción de saltar wizard en settings (con advertencia)

### Nueva funcionalidad:
- Si el cliente no tiene alergias/tratamientos químicos → Saltar directo al consentimiento
- Pre-llenar test de parche si ya se hizo antes
- Mostrar historial de consentimientos previos

## Sistema Coherente de Unidades - 2025-07-08

### Estado: COMPLETADO ✅

**Objetivo**: Mantener coherencia en el sistema de unidades entre stock base y envases.

### Solución implementada:
- ✅ Stock SIEMPRE en unidades base (ml para líquidos, g para pólvos)
- ✅ UI muestra ambos: unidades base y equivalencia en envases
- ✅ Conversión automática al consumir productos
- ✅ Validaciones mejoradas en formularios

### Componentes actualizados:
- `stores/inventory-store.ts`: Lógica de conversión centralizada
- `components/inventory/ProductEditModal.tsx`: UI dual mejorada
- `utils/inventoryConsumptionService.ts`: Cálculos precisos

## Niveles de Inventario Coherentes - 2025-07-08

### Estado: COMPLETADO ✅

**Objetivo**: Asegurar que cada nivel de inventario funcione correctamente según su propósito.

### Comportamiento por nivel:
1. **Solo Fórmulas**: 
   - ✅ NO gestión de inventario
   - ✅ Muestra costes ESTIMADOS
   - ✅ NO verifica stock
   - ✅ NO consume productos

2. **Smart Cost**:
   - ✅ Gestión básica de inventario
   - ✅ Muestra costes REALES del inventario
   - ✅ NO verifica stock disponible
   - ✅ NO consume automáticamente

3. **Control Total**:
   - ✅ Gestión completa
   - ✅ Costes reales
   - ✅ VERIFICA stock antes del servicio
   - ✅ CONSUME automáticamente al completar

## Diseño Minimalista Blanco - 2025-07-09

### Estado: COMPLETADO ✅

**Objetivo**: Unificar toda la aplicación con un diseño minimalista basado en blanco.

### Cambios globales:
- ✅ Fondo base #FFFFFF en TODAS las pantallas
- ✅ Eliminación de fondos grises (#F5F5F7)
- ✅ Cards sin bordes, solo sombras sutiles
- ✅ Navegación clara con contraste mínimo
- ✅ Botones con colores sólidos

### Componentes actualizados:
- `constants/colors.ts`: background = '#FFFFFF'
- Todas las pantallas principales
- Modales y overlays
- Sistema de navegación

## InstructionsFlow Premium - 2025-07-05

### Estado: COMPLETADO ✅

**Objetivo**: Crear un flujo de instrucciones paso a paso premium para guiar el proceso de coloración.

### Pantallas implementadas (10):
1. ✅ **ChecklistScreen**: Lista de verificación organizada por tipo
2. ✅ **TransformationScreen**: Visualización del cambio de color
3. ✅ **FormulasScreen**: Fórmulas por zona con selector visual
4. ✅ **ProportionsScreen**: Guía de proporciones con referencias
5. ✅ **CalculatorScreen**: Calculadora visual con siluetas
6. ✅ **MixingScreen**: Estación de mezcla animada
7. ✅ **ApplicationScreen**: Guía multi-zona de aplicación
8. ✅ **TimelineScreen**: Cronograma del proceso
9. ✅ **TipsScreen**: Tips categorizados en cards
10. ✅ **ResultScreen**: Resultado esperado con métricas

### Características del diseño:
- Gradientes premium y animaciones sutiles
- Navegación fluida entre pasos
- Componentes visuales interactivos
- Diseño coherente con marca

## Migración a CameraView - 2025-07-05

### Estado: COMPLETADO ✅

**Objetivo**: Migrar de Camera (deprecated) a CameraView de expo-camera.

### Cambios realizados:
- ✅ Actualizar todas las importaciones
- ✅ Migrar props y métodos
- ✅ Resolver problema de children en overlay
- ✅ Mantener funcionalidad de guías visuales

### Solución al problema de children:
```jsx
<View style={styles.container}>
  <CameraView style={styles.camera} />
  <View style={StyleSheet.absoluteFillObject}>
    {/* overlay content */}
  </View>
</View>
```