# Changelog - <PERSON><PERSON> Copilot

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.1] - 2025-01-11 - OPTIMIZACIÓN COMPLETA DE SUPABASE

### ✅ ESTADO: Optimizaciones de performance y seguridad completadas

### Added
- 🚀 **Optimizaciones de Performance**:
  - 9 políticas RLS optimizadas con `(SELECT auth.uid())` para evaluación única
  - 5 índices creados en foreign keys para búsquedas más rápidas
  - 4 constraints de validación para integridad de datos
  - 5 triggers automáticos para mantener campos `updated_at`
  - Función `cleanup_expired_ai_cache()` para limpieza automática

- 🔒 **Mejoras de Seguridad**:
  - 4 funciones aseguradas con `SECURITY DEFINER SET search_path = public`
  - Validación de stock no negativo
  - Validación de formato de email
  - Validación de fechas de servicio
  - Validación de permisos permitidos

- 🤖 **Edge Function Actualizada**:
  - Modelo actualizado de `gpt-4-vision-preview` a `gpt-4o`
  - Retry logic con exponential backoff para rate limiting
  - Cálculo de costos actualizado con precios de 2025
  - Mejor manejo de errores y validación de API key
  - Soporte para `response_format: { type: "json_object" }`

- 📁 **Nuevos Scripts y Documentación**:
  - `SUPABASE_OPTIMIZATION_SUMMARY.md` - Resumen detallado de optimizaciones
  - `scripts/supabase-dashboard-setup.md` - Guía para configuración manual
  - `scripts/validate-optimizations.sql` - Script de validación
  - Migraciones 011 y 012 sincronizadas desde Supabase

### Changed
- 🎯 **11 índices no utilizados eliminados** para liberar recursos
- 📊 **Migraciones aplicadas**: 009, 013, 014
- 🔄 **Edge Function `salonier-assistant`** actualizada (versión 2)

### Fixed
- ⚡ **Performance de RLS** mejorado significativamente
- 🛡️ **Vulnerabilidades de path injection** en funciones corregidas
- 📈 **Consultas optimizadas** con índices apropiados

### Security
- Funciones protegidas contra ataques de SQL injection
- Constraints previenen datos inválidos a nivel de base de datos
- Edge Function con mejor manejo de rate limiting

## [2.0.0] - 2025-07-11 - MIGRACIÓN SUPABASE COMPLETA

### ✅ ESTADO: Migración a Supabase completada exitosamente

### Added
- 🌐 **Integración completa con Supabase**:
  - Base de datos PostgreSQL configurada con 9 tablas
  - Sistema de autenticación con Supabase Auth
  - Edge Functions para procesamiento de IA
  - Row Level Security (RLS) para multi-tenancy
  - Triggers y funciones automáticas con fallback manual

- 🔄 **Sistema offline-first con sincronización**:
  - Patrón UI Optimistic en todos los stores
  - Cola de sincronización para operaciones offline
  - Indicadores visuales de estado de conexión
  - Detección automática de red con @react-native-community/netinfo

- 💾 **Todos los stores migrados a Supabase**:
  - auth-store: Autenticación completa con Supabase Auth
  - sync-queue-store: Gestión de cola offline
  - client-store: Clientes con UI optimistic
  - inventory-store: Inventario con sincronización
  - client-history-store: Historial con sincronización
  - ai-analysis-store: Análisis con Edge Functions
  - team-store: Gestión de equipo con sincronización
  - salon-config-store: Configuración del salón sincronizada

- 📊 **Nuevos componentes de sincronización**:
  - SyncIndicator: Muestra estado de conexión y sincronización
  - Integración en BaseHeader
  - Modal con detalles de sincronización

- 🛡️ **Sistema robusto de registro**:
  - Polling para esperar creación de perfil/salón
  - Fallback manual si el trigger SQL falla
  - Función manual_user_setup para casos especiales
  - Mejores mensajes de error y recuperación

### Changed
- 🔧 **Arquitectura de stores rediseñada**:
  - Todos los stores usan patrón UI Optimistic
  - IDs temporales para operaciones offline
  - Funciones de conversión local <-> Supabase
  - Persistencia selectiva (solo datos no sincronizados)

- 🔐 **Sistema de autenticación actualizado**:
  - Login/registro con Supabase Auth
  - Inicialización automática de sesión
  - Sincronización post-login de todos los stores
  - Manejo mejorado de errores de configuración

- 🌐 **Análisis IA migrado a Edge Functions**:
  - MockFormulationService reemplazado
  - Imágenes comprimidas antes de subir
  - Procesamiento en servidor para proteger API keys
  - Cliente dedicado para Edge Functions

### Fixed
- ✅ **"No salon ID found"**: Eliminado auto-sync al inicializar stores
- ✅ **"Invalid API key"**: Mensajes de error mejorados con instrucciones
- ✅ **Sincronización**: Solo ocurre después de autenticación exitosa
- ✅ **Recursión infinita en RLS**: Políticas corregidas y simplificadas
- ✅ **Error de registro**: Implementado fallback robusto con manual_user_setup
- ✅ **Payload de IA**: Corregida estructura de datos para Edge Function

### Dependencies Added
- `@react-native-community/netinfo`: Detección de estado de red
- `expo-image-manipulator`: Compresión de imágenes
- `expo-file-system`: Lectura de archivos como base64

### Configuration
- ✅ **Supabase configurado y funcionando**:
  - URL: https://ajsamgugqfbttkrlgvbr.supabase.co
  - Clave anon configurada en .env.local
  - Proyecto activo y funcionando
  - Edge Functions desplegadas

### Migration Completed
- ✅ Autenticación funcionando perfectamente
- ✅ Registro de usuarios con creación automática de salón
- ✅ Todos los stores migrados y sincronizando
- ✅ Edge Functions configuradas para análisis IA
- ✅ RLS policies funcionando correctamente
- ✅ Sistema offline-first completamente operativo

### Files Modified
- `/lib/supabase.ts`: Cliente de Supabase configurado
- `/lib/edge-functions.ts`: Cliente para Edge Functions
- `/stores/*.ts`: Todos los stores migrados con UI Optimistic
- `/app/_layout.tsx`: Inicialización de auth actualizada
- `/app/auth/*.tsx`: Pantallas de auth con Supabase
- `/app/service/new.tsx`: Usando Edge Functions para IA
- `/components/SyncIndicator.tsx`: Nuevo componente
- `/.env.local`: Credenciales de Supabase configuradas
- `/supabase/migrations/`: 10 archivos de migración SQL

### Documentation
- `README_SETUP.md`: Guía completa de configuración
- `SETUP_ENV.md`: Instrucciones para variables de entorno
- `todo.md`: Estado actualizado del proyecto
- `IMPLEMENTATION_SUMMARY.md`: Resumen completo de la migración
- `Claude.md`: Documentación de trabajo actualizada

## [1.3.0] - 2025-07-05

### Added
- 🎨 **InstructionsFlow Premium**: Nuevo flujo de instrucciones paso a paso con 10 pantallas interactivas
  - ChecklistScreen: Lista de verificación con agrupación por tipo de producto (tintes, oxidantes, aditivos)
  - TransformationScreen: Visualización mejorada de transformación de color con círculos centrados
  - FormulasScreen: Fórmulas personalizadas por zona con selector interactivo
  - ProportionsScreen: Guía visual de proporciones con referencia rápida
  - CalculatorScreen: Calculadora visual con siluetas de cabello y layout horizontal
  - MixingScreen: Estación de mezcla con animación mejorada y burbujas decorativas
  - ApplicationScreen: Guía de aplicación con soporte para múltiples zonas (raíces, medios, puntas, corona, nuca)
  - TimelineScreen: Cronograma del proceso sin timer activo
  - TipsScreen: Tips profesionales en cards visuales con categorización
  - ResultScreen: Resultado esperado con diseño premium, hero section y métricas visuales
- 📱 Botón prominente "Ver Instrucciones Paso a Paso" en FormulaVisualization

### Changed
- 🔄 FormulaVisualization simplificado - removido contenido técnico para enfocarse en visualización
- 🎨 ProportionCalculator mejorado con detección automática de proporciones y visualización por bloques
- 🚀 Diseño visual general actualizado con gradientes premium y animaciones sutiles
- 📐 Aplicada regla 60-30-10 de color para mejor jerarquía visual

### Removed
- 🗑️ StepByStepGuide.tsx completamente eliminado (reemplazado por InstructionsFlow)
- 🗑️ Sistema antiguo de tabs en FormulaVisualization
- 🗑️ Contenido técnico redundante en vista de formulación

### Fixed
- 🐛 Bug de renderizado innecesario en FormulaVisualization
- 🐛 Keys duplicadas en ProportionCalculator (línea 190)
- 🐛 Centrado de elementos en TransformationScreen
- 🐛 Decimales excesivos en niveles de cambio (ahora usa toFixed(0))
- 🐛 Layout "escalera" en CalculatorScreen reemplazado por diseño horizontal

### Technical
- 📦 Nuevo componente: components/formulation/InstructionsFlow.tsx (2574 líneas)
- 🔧 Trabajando con datos mock para perfeccionar la visión antes de conectar con AI
- 💾 Preparado para integración futura con prompts AI para contenido dinámico

## [1.2.4] - 2025-07-04

### Changed
- 🎨 Simplificación completa de Configuración de IA
  - Reducido a 2 controles esenciales: Nivel de Análisis y Modo Privacidad
  - Eliminado modal de "Configuración Avanzada" innecesario
  - Nuevo selector visual con tiempos estimados (30s/2min/5min)
  - Descripción contextual según selección

### Fixed
- 🐛 Eliminada sección duplicada "Privacidad y Seguridad"
- 🐛 Removidas opciones sin funcionalidad real (Modo Oscuro, Respaldo Automático)

### Enhanced
- ✨ Rediseño completo de sección "Notificaciones"
  - Opciones relevantes: Recordatorios de Servicios, Alertas de Inventario, Tips
  - Descripciones claras para cada opción
  - Alertas de inventario solo visibles cuando está activo
  
- ✨ Nueva sección "Datos y Respaldo"
  - Exportar datos (CSV/PDF)
  - Enlaces a políticas y términos
  - Opción de eliminar todos los datos

### Improved
- 📝 Nomenclatura más clara
  - "Configuración del Sistema" → "Configuración Regional"
  - "Información Profesional" → "Mi Licencia Profesional"
  - Textos más concisos y directos

## [1.2.3] - 2025-07-03

### Enhanced
- 🎨 Mejorada UX de conversión de marcas
  - Título más claro: "Adaptar fórmula de otra marca"
  - Eliminada fricción innecesaria (pregunta sobre motivo)
  - Labels con preguntas naturales y directas
  - Flujo reordenado para mayor intuitividad
  - Feedback visual mejorado

### Verified
- ✅ Aplicación sigue 100% funcional
- ✅ Conversión de marcas más intuitiva
- ✅ Usuario reporta funcionamiento correcto

## [1.2.2] - 2025-07-03 - VERSIÓN ESTABLE ✅

### Fixed
- 🐛 Resuelto error de cálculo de coste "color.amount.replace is not a function"
  - Corregidas inconsistencias de tipos en `inventoryConsumptionService.ts`
  - Actualizado parseo para manejar `amount` como número
  - Mejorado manejo de estados de cámara para evitar race conditions residuales

### Verified
- ✅ **Aplicación 100% funcional**
- ✅ Cámara funciona correctamente en todas las fases
- ✅ Cálculo de costes operativo
- ✅ Flujo completo desde diagnóstico hasta formulación sin errores
- ✅ Usuario confirmó funcionamiento correcto de todas las características

## [1.2.1] - 2025-07-03

### Fixed
- 🐛 **CRÍTICO**: Resuelto crash de cámara en fase "Color Deseado"
  - Problema: Race condition entre establecimiento de estados y apertura de cámara
  - Solución: Implementada sincronización correcta con `pendingCameraOpen` y useEffect
  - Verificado: Flujo completo funciona correctamente hasta formulación

## [1.2.0] - 2025-07-03

### Added
- Sistema de conversión inteligente entre marcas con base de datos real
- Corrección de color automática con detección de matices no deseados
- Diagnóstico capilar profesional con análisis por zonas
- UI mejorada para conversión con comunicación clara del propósito

### Enhanced
- Navegación robusta sin bloqueos post-firma
- Manejo completo de errores y estados asíncronos
- Validaciones exhaustivas en todos los flujos

## [1.1.0] - 2025-07-02

### Added
- Análisis IA unificado con un solo botón
- Niveles decimales de precisión (1.0-10.0)
- Pre-llenado inteligente de campos desde historial
- Modal de selección de marca interactivo

### Fixed
- Errores de navegación al finalizar servicio
- Problemas de parseo en fórmulas complejas