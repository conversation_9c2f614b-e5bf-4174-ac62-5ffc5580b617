{"name": "expo-app", "main": "expo-router/entry", "version": "2.0.1", "scripts": {"start": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --tunnel", "start-web": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "mobile": "expo start --lan --clear", "mobile:stable": "expo start --lan", "mobile:tunnel": "expo start --tunnel", "dev": "expo start"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "v2.0.0-next.4", "@supabase/supabase-js": "^2.50.5", "expo": "^53.0.4", "expo-blur": "~14.1.4", "expo-camera": "^16.1.10", "expo-constants": "~17.1.4", "expo-crypto": "^14.1.5", "expo-file-system": "~18.1.11", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.0.3", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}