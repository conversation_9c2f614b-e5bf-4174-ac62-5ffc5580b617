import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { router } from 'expo-router';
import { ChevronLeft, Save } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { STEPS } from '@/src/service/hooks/useServiceFlow';

interface ServiceHeaderProps {
  currentStep: number;
  clientName?: string;
  onSaveDraft?: () => void;
  onBack?: () => void;
}

export const ServiceHeader: React.FC<ServiceHeaderProps> = ({
  currentStep,
  clientName,
  onSaveDraft,
  onBack
}) => {
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  const handleSaveDraft = () => {
    if (onSaveDraft) {
      Alert.alert(
        "Guardar borrador",
        "¿Deseas guardar el progreso actual como borrador?",
        [
          { text: "Cancelar", style: "cancel" },
          { 
            text: "Guardar", 
            onPress: () => {
              onSaveDraft();
              Alert.alert("Borrador guardado", "Podrás continuar más tarde desde donde lo dejaste.");
            }
          }
        ]
      );
    }
  };

  const currentStepTitle = STEPS[currentStep]?.title || "Servicio";

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleBack}
        >
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{currentStepTitle}</Text>
          {clientName && (
            <Text style={styles.subtitle}>{clientName}</Text>
          )}
        </View>
        
        {onSaveDraft && (
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSaveDraft}
          >
            <Save size={20} color={Colors.light.primary} />
          </TouchableOpacity>
        )}
        
        {!onSaveDraft && <View style={styles.placeholder} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    minHeight: 60,
  },
  backButton: {
    padding: 5,
    marginRight: 10,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
    textAlign: 'center',
  },
  saveButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.primary + '10',
    marginLeft: 10,
  },
  placeholder: {
    width: 36,
    marginLeft: 10,
  },
});
