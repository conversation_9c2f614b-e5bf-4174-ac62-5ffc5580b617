import React, { useState, useRef, useEffect } from 'react';
import type { ScrollView } from 'react-native';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { Eye, Shield, Zap } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import {
  HairZone,
  NaturalTone,
  Undertone,
  HairState,
  GrayHairType,
  GrayPattern,
  UnwantedTone,
  CuticleState,
  HairPorosity,
  HairElasticity,
  HairResistance,
} from '@/types/hair-diagnosis';
import { PhotoAngle, PHOTO_GUIDES, PhotoGuide, PhotoQuality } from '@/types/photo-capture';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import { calculateDiagnosisProgress } from '@/src/service/utils/serviceValidations';
import { SwipeableScreen } from '@/components/navigation/SwipeableScreen';
import { ScrollableContent } from '@/components/navigation/ScrollableContent';

// Types for recommendations
interface Recommendation {
  type: 'brand_preference' | 'technique' | 'product';
  value: string;
  confidence: number;
  reason?: string;
}

// Import existing components
import ClientHistoryPanel from '@/components/ClientHistoryPanel';
import DiagnosisSelector from '@/components/DiagnosisSelector';
import DiagnosisTextInput from '@/components/DiagnosisTextInput';
import ZoneDiagnosisForm from '@/components/ZoneDiagnosisForm';
import PhotoGallery from '@/components/PhotoGallery';
import { ZoneAnalysisDisplay } from '@/components/base';
import AIResultNotification from '@/components/AIResultNotification';

interface DiagnosisStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
}

export const DiagnosisStep: React.FC<DiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave,
  onSaveSilent,
}) => {
  const [diagnosisTab, setDiagnosisTab] = useState<'roots' | 'mids' | 'ends'>('roots');
  const [_showHistoryPanel, _setShowHistoryPanel] = useState(true);
  const [isDataFromAI, setIsDataFromAI] = useState(false);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);

  const _isMounted = useRef(true);
  const hasShownNotificationRef = useRef(false);

  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);

  const {
    isAnalyzing,
    analysisResult,
    privacyMode: _privacyMode,
    setPrivacyMode: _setPrivacyMode,
    clearAnalysis: _clearAnalysis,
    performAnalysis,
    pickMultipleImages,
    takePhoto,
    performImageQualityCheck,
  } = usePhotoAnalysis();

  // Reset AI-related states when client changes or component mounts
  useEffect(() => {
    setIsDataFromAI(false);
    setShowAINotification(false);
    setAIFieldsCount(0);
    hasShownNotificationRef.current = false;
  }, [data.clientId]);

  // Effect to handle AI analysis results
  useEffect(() => {
    if (analysisResult && !isAnalyzing && !hasShownNotificationRef.current) {
      // Update fields with AI results
      onUpdate({
        hairThickness: analysisResult.hairThickness,
        hairDensity: analysisResult.hairDensity,
        overallTone: analysisResult.overallTone,
        overallReflect: analysisResult.overallReflect || analysisResult.overallUndertone,
        lastChemicalProcessType:
          analysisResult.detectedChemicalProcess || data.lastChemicalProcessType,
        lastChemicalProcessDate:
          analysisResult.estimatedLastProcessDate || data.lastChemicalProcessDate,
        zoneColorAnalysis: {
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            level:
              analysisResult.zoneAnalysis.roots?.depth ||
              analysisResult.zoneAnalysis.roots?.level ||
              5,
            tone: analysisResult.zoneAnalysis.roots?.tone as NaturalTone,
            reflect:
              analysisResult.zoneAnalysis.roots?.reflect ||
              (analysisResult.zoneAnalysis.roots?.undertone as Undertone),
            state: analysisResult.zoneAnalysis.roots?.state as HairState,
            grayPercentage: analysisResult.zoneAnalysis.roots?.grayPercentage || 0,
            grayType: analysisResult.zoneAnalysis.roots?.grayType as GrayHairType,
            grayPattern: analysisResult.zoneAnalysis.roots?.grayPattern as GrayPattern,
            unwantedTone: analysisResult.zoneAnalysis.roots?.unwantedTone as UnwantedTone,
            cuticleState: analysisResult.zoneAnalysis.roots?.cuticleState as CuticleState,
            damage: analysisResult.zoneAnalysis.roots?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            level:
              analysisResult.zoneAnalysis.mids?.depth ||
              analysisResult.zoneAnalysis.mids?.level ||
              5,
            tone: analysisResult.zoneAnalysis.mids?.tone as NaturalTone,
            reflect:
              analysisResult.zoneAnalysis.mids?.reflect ||
              (analysisResult.zoneAnalysis.mids?.undertone as Undertone),
            state: analysisResult.zoneAnalysis.mids?.state as HairState,
            unwantedTone: analysisResult.zoneAnalysis.mids?.unwantedTone as UnwantedTone,
            pigmentAccumulation: analysisResult.zoneAnalysis.mids?.pigmentAccumulation as
              | 'Baja'
              | 'Media'
              | 'Alta',
            cuticleState: analysisResult.zoneAnalysis.mids?.cuticleState as CuticleState,
            damage: analysisResult.zoneAnalysis.mids?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            level:
              analysisResult.zoneAnalysis.ends?.depth ||
              analysisResult.zoneAnalysis.ends?.level ||
              5,
            tone: analysisResult.zoneAnalysis.ends?.tone as NaturalTone,
            reflect:
              analysisResult.zoneAnalysis.ends?.reflect ||
              (analysisResult.zoneAnalysis.ends?.undertone as Undertone),
            state: analysisResult.zoneAnalysis.ends?.state as HairState,
            unwantedTone: analysisResult.zoneAnalysis.ends?.unwantedTone as UnwantedTone,
            pigmentAccumulation: analysisResult.zoneAnalysis.ends?.pigmentAccumulation as
              | 'Baja'
              | 'Media'
              | 'Alta',
            cuticleState: analysisResult.zoneAnalysis.ends?.cuticleState as CuticleState,
            damage: analysisResult.zoneAnalysis.ends?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
        },
        zonePhysicalAnalysis: {
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            porosity: analysisResult.zoneAnalysis.roots?.porosity as HairPorosity,
            elasticity: analysisResult.zoneAnalysis.roots?.elasticity as HairElasticity,
            resistance: analysisResult.zoneAnalysis.roots?.resistance as HairResistance,
            damage: analysisResult.zoneAnalysis.roots?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            porosity: analysisResult.zoneAnalysis.mids?.porosity as HairPorosity,
            elasticity: analysisResult.zoneAnalysis.mids?.elasticity as HairElasticity,
            resistance: analysisResult.zoneAnalysis.mids?.resistance as HairResistance,
            damage: analysisResult.zoneAnalysis.mids?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            porosity: analysisResult.zoneAnalysis.ends?.porosity as HairPorosity,
            elasticity: analysisResult.zoneAnalysis.ends?.elasticity as HairElasticity,
            resistance: analysisResult.zoneAnalysis.ends?.resistance as HairResistance,
            damage: analysisResult.zoneAnalysis.ends?.damage as 'Bajo' | 'Medio' | 'Alto',
          },
        },
      });

      setIsDataFromAI(true);
      // Save silently without showing toast
      onSaveSilent?.();

      // Count fields that were filled
      let fieldsCount = 0;
      if (analysisResult.condition) fieldsCount++;
      if (analysisResult.texture) fieldsCount++;
      if (analysisResult.hairThickness) fieldsCount++;
      if (analysisResult.hairDensity) fieldsCount++;
      if (analysisResult.overallTone) fieldsCount++;
      if (analysisResult.overallReflect) fieldsCount++;
      // Add zone analysis fields
      fieldsCount += Object.keys(analysisResult.zoneAnalysis).length * 2; // color + physical per zone

      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Smooth scroll to first result section after a short delay
      if (scrollRef.current) {
        setTimeout(() => {
          scrollRef.current?.scrollTo({ y: 200, animated: true });
        }, 300);
      }
    }

    // Reset the flag when analysis is cleared
    if (!analysisResult) {
      hasShownNotificationRef.current = false;
    }
  }, [analysisResult, isAnalyzing]);

  const handleAIAnalysis = async () => {
    if (data.hairPhotos.length === 0) {
      Alert.alert('Error', 'Por favor captura al menos una foto del cabello');
      return;
    }

    try {
      await performAnalysis(data.hairPhotos, data.clientId!);
    } catch (error: unknown) {
      const errorAsError = error as Error;
      if (errorAsError.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          'Análisis tardando más de lo normal',
          'El análisis está tardando más de 30 segundos. ¿Qué deseas hacer?',
          [
            {
              text: 'Continuar esperando',
              onPress: () => handleAIAnalysis(),
            },
            {
              text: 'Diagnóstico manual',
              onPress: () => {
                onUpdate({ diagnosisMethod: 'manual' });
              },
              style: 'cancel',
            },
          ]
        );
      } else {
        Alert.alert(
          'Error en el análisis',
          'No se pudo completar el análisis. Por favor intenta nuevamente.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  const handleMultipleImagePick = async () => {
    const message = await pickMultipleImages(
      data.hairPhotos,
      photos => {
        onUpdate({ hairPhotos: photos });
      },
      onSave
    );

    if (message) {
      // Show toast message (you might want to pass this up to parent)
      // Debug logging removed for production
    }
  };

  const handleCameraOpen = async () => {
    try {
      await takePhoto(async uri => {
        // Determine the next angle for the photo
        const capturedAngles = data.hairPhotos.map(p => p.angle);
        const nextGuide = PHOTO_GUIDES.find((g: PhotoGuide) => !capturedAngles.includes(g.angle));
        const angle = nextGuide?.angle || PhotoAngle.CROWN;

        // Create quality assessment
        const quality = performImageQualityCheck(uri);
        const qualityObj: PhotoQuality = {
          lighting: quality.isGoodLighting ? 'good' : 'fair',
          focus: quality.isInFocus ? 'good' : 'fair',
          stability: 'good',
          overall: quality.overallScore,
        };

        // Create new photo
        const newPhoto = {
          id: Date.now().toString(),
          uri,
          angle,
          quality: qualityObj,
          timestamp: new Date(),
        };

        // Update photos
        const updatedPhotos = [...data.hairPhotos, newPhoto];
        onUpdate({ hairPhotos: updatedPhotos });
        onSave?.();
      });
    } catch (error) {
      logger.error('Error taking photo:', error);
    }
  };

  const handleRecommendationApply = (recommendation: Recommendation) => {
    // Apply recommendation to current data
    if (recommendation.type === 'brand_preference') {
      onUpdate({ selectedBrand: recommendation.value });
    }
    // Add more recommendation types as needed
  };

  const renderPrivacyBanner = () => (
    <View style={styles.privacyBanner}>
      <Shield size={16} color={Colors.light.success} />
      <Text style={styles.privacyBannerText}>
        🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan
        inmediatamente después del análisis.
      </Text>
    </View>
  );

  const currentZone =
    diagnosisTab === 'roots'
      ? HairZone.ROOTS
      : diagnosisTab === 'mids'
        ? HairZone.MIDS
        : HairZone.ENDS;

  const progressData = calculateDiagnosisProgress(
    data.hairThickness,
    data.hairDensity,
    data.overallTone,
    data.overallUndertone || data.overallReflect || '',
    data.zoneColorAnalysis,
    data.zonePhysicalAnalysis,
    data.hairPhotos
  );

  return (
    <>
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Análisis completado con IA"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to the results section with a more noticeable offset
            scrollRef.current.scrollTo({ y: 400, animated: true });
            // Add haptic feedback when scrolling
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <SwipeableScreen
        onSwipeLeft={progressData.canProceed ? onNext : undefined}
        swipeEnabled={progressData.canProceed}
        scrollPriority="vertical"
        style={styles.container}
      >
        <ScrollableContent ref={scrollRef}>
          <View style={styles.stepContainer}>
            <View style={styles.diagnosisHeader}>
              <View>
                <Text style={styles.stepTitle}>Diagnóstico Capilar Ultra-Inteligente</Text>
                {data.client && (
                  <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
                )}
              </View>
            </View>

            {renderPrivacyBanner()}

            {/* Client History Panel */}
            {data.clientId && _showHistoryPanel && (
              <ClientHistoryPanel
                clientId={data.clientId}
                onRecommendationApply={handleRecommendationApply}
              />
            )}

            <View style={styles.tabsContainer}>
              <TouchableOpacity
                style={[styles.tab, data.diagnosisMethod === 'ai' && styles.activeTab]}
                onPress={() => onUpdate({ diagnosisMethod: 'ai' })}
              >
                <Zap
                  size={16}
                  color={data.diagnosisMethod === 'ai' ? Colors.light.primary : Colors.light.gray}
                />
                <Text
                  style={[styles.tabText, data.diagnosisMethod === 'ai' && styles.activeTabText]}
                >
                  Con IA ✨
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, data.diagnosisMethod === 'manual' && styles.activeTab]}
                onPress={() => onUpdate({ diagnosisMethod: 'manual' })}
              >
                <Eye
                  size={16}
                  color={
                    data.diagnosisMethod === 'manual' ? Colors.light.primary : Colors.light.gray
                  }
                />
                <Text
                  style={[
                    styles.tabText,
                    data.diagnosisMethod === 'manual' && styles.activeTabText,
                  ]}
                >
                  Manual
                </Text>
              </TouchableOpacity>
            </View>

            {/* AI Mode */}
            {data.diagnosisMethod === 'ai' && (
              <>
                <Text style={styles.sectionTitle}>Fotografías del cabello (3-5 imágenes)</Text>

                <PhotoGallery
                  photos={data.hairPhotos}
                  onAddPhoto={handleMultipleImagePick}
                  onCameraCapture={handleCameraOpen}
                  onRemovePhoto={photoId => {
                    const updatedPhotos = data.hairPhotos.filter(p => p.id !== photoId);
                    onUpdate({ hairPhotos: updatedPhotos });
                  }}
                  maxPhotos={5}
                  showQuality={true}
                />

                {data.hairPhotos.length > 0 && (
                  <TouchableOpacity
                    style={[styles.aiButton, isAnalyzing && styles.aiButtonDisabled]}
                    onPress={handleAIAnalysis}
                    disabled={isAnalyzing}
                  >
                    {isAnalyzing ? (
                      <>
                        <ActivityIndicator size="small" color="white" />
                        <Text style={styles.aiButtonText}>Analizando cabello...</Text>
                      </>
                    ) : (
                      <>
                        <Zap size={16} color="white" />
                        <Text style={styles.aiButtonText}>Analizar con IA</Text>
                      </>
                    )}
                  </TouchableOpacity>
                )}
              </>
            )}

            {/* Manual Mode or AI Results */}
            {(data.diagnosisMethod === 'manual' || analysisResult) && (
              <>
                {/* Chemical History */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Historial Químico</Text>

                  <DiagnosisTextInput
                    label="Último proceso químico"
                    value={data.lastChemicalProcessType || ''}
                    onChangeText={value => onUpdate({ lastChemicalProcessType: value })}
                    placeholder="Ej: Decoloración, Tinte permanente, Alisado..."
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisTextInput
                    label="Fecha del último proceso"
                    value={data.lastChemicalProcessDate || ''}
                    onChangeText={value => onUpdate({ lastChemicalProcessDate: value })}
                    placeholder="Ej: 15/05/2024"
                    isFromAI={isDataFromAI}
                  />

                  <View style={styles.switchGroup}>
                    <Text style={styles.inputLabel}>¿Ha usado remedios caseros?</Text>
                    <Switch
                      value={data.hasUsedHomeRemedies || false}
                      onValueChange={value => onUpdate({ hasUsedHomeRemedies: value })}
                      trackColor={{
                        false: Colors.light.lightGray,
                        true: Colors.light.primary,
                      }}
                      thumbColor={data.hasUsedHomeRemedies ? 'white' : Colors.light.gray}
                    />
                  </View>
                </View>

                {/* Physical Measurements */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Mediciones Físicas</Text>

                  <DiagnosisTextInput
                    label="Longitud total del cabello (cm)"
                    value={data.hairLength?.toString() || ''}
                    onChangeText={value => onUpdate({ hairLength: parseFloat(value) || 0 })}
                    placeholder="0.0 cm"
                    keyboardType="numeric"
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisTextInput
                    label="Crecimiento mensual (cm)"
                    value={data.monthlyGrowth?.toString() || ''}
                    onChangeText={value => onUpdate({ monthlyGrowth: parseFloat(value) || 0 })}
                    placeholder="1.25"
                    keyboardType="numeric"
                    isFromAI={isDataFromAI}
                  />
                </View>

                {/* General characteristics */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Características Generales</Text>

                  <DiagnosisSelector
                    label="Grosor del cabello"
                    value={data.hairThickness}
                    options={['Fino', 'Medio', 'Grueso']}
                    onValueChange={value => onUpdate({ hairThickness: value })}
                    required
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisSelector
                    label="Densidad del cabello"
                    value={data.hairDensity}
                    options={['Baja', 'Media', 'Alta']}
                    onValueChange={value => onUpdate({ hairDensity: value })}
                    required
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisSelector
                    label="Tono predominante"
                    value={data.overallTone}
                    options={[
                      'Rubio platino',
                      'Rubio claro',
                      'Rubio medio',
                      'Rubio oscuro',
                      'Castaño claro',
                      'Castaño medio',
                      'Castaño oscuro',
                      'Negro',
                    ]}
                    onValueChange={value => onUpdate({ overallTone: value })}
                    required
                    isFromAI={isDataFromAI}
                    showColorIndicator={true}
                  />

                  <DiagnosisSelector
                    label="Reflejo predominante"
                    value={data.overallReflect}
                    options={['Frío', 'Cálido', 'Neutro']}
                    onValueChange={value => onUpdate({ overallReflect: value })}
                    required
                    isFromAI={isDataFromAI}
                    showColorIndicator={true}
                    showTemperatureIndicator={true}
                  />
                </View>

                {/* Zone tabs */}
                <ZoneAnalysisDisplay
                  currentZone={currentZone}
                  onZoneChange={zone => {
                    if (zone === HairZone.ROOTS) setDiagnosisTab('roots');
                    else if (zone === HairZone.MIDS) setDiagnosisTab('mids');
                    else if (zone === HairZone.ENDS) setDiagnosisTab('ends');
                  }}
                  completedZones={{
                    [HairZone.ROOTS]: !!data.zoneColorAnalysis[HairZone.ROOTS]?.level,
                    [HairZone.MIDS]: !!data.zoneColorAnalysis[HairZone.MIDS]?.level,
                    [HairZone.ENDS]: !!data.zoneColorAnalysis[HairZone.ENDS]?.level,
                  }}
                  showCompletionIndicators={true}
                />

                {/* Zone-specific diagnosis */}
                {currentZone && (
                  <ZoneDiagnosisForm
                    zone={currentZone}
                    colorAnalysis={data.zoneColorAnalysis[currentZone] || {}}
                    physicalAnalysis={data.zonePhysicalAnalysis[currentZone] || {}}
                    onColorChange={analysis => {
                      onUpdate({
                        zoneColorAnalysis: {
                          ...data.zoneColorAnalysis,
                          [currentZone]: {
                            ...data.zoneColorAnalysis[currentZone],
                            ...analysis,
                          },
                        },
                      });
                    }}
                    onPhysicalChange={analysis => {
                      onUpdate({
                        zonePhysicalAnalysis: {
                          ...data.zonePhysicalAnalysis,
                          [currentZone]: {
                            ...data.zonePhysicalAnalysis[currentZone],
                            ...analysis,
                          },
                        },
                      });
                    }}
                    isFromAI={isDataFromAI}
                  />
                )}
              </>
            )}

            {/* Continue Button */}
            {(data.diagnosisMethod === 'manual' || analysisResult) && (
              <TouchableOpacity style={styles.continueButton} onPress={onNext}>
                <Text style={styles.continueButtonText}>Continuar al Color Deseado</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollableContent>
      </SwipeableScreen>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  section: {
    marginBottom: 20,
  },
  diagnosisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  privacyBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.success + '15',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.success,
  },
  privacyBannerText: {
    fontSize: 12,
    color: Colors.light.success,
    marginLeft: 8,
    flex: 1,
    fontWeight: '500',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 4,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    borderRadius: 12,
  },
  activeTab: {
    backgroundColor: 'white',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: '700',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  aiButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  aiButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  aiButtonText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 16,
  },
  continueButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500',
  },
});
