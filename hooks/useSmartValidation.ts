/**
 * Smart Validation Hook for Salonier
 * Provides real-time, non-intrusive form validation
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import * as Haptics from 'expo-haptics';

export interface ValidationRule<T = unknown> {
  validate: (value: T) => boolean;
  message: string;
  severity?: 'error' | 'warning' | 'info';
}

export interface FieldValidation<T = unknown> {
  value: T;
  rules: ValidationRule<T>[];
  required?: boolean;
  requiredMessage?: string;
}

export interface ValidationState {
  isValid: boolean;
  isDirty: boolean;
  isTouched: boolean;
  errors: string[];
  warnings: string[];
  info: string[];
}

export interface UseSmartValidationReturn<T> {
  values: T;
  errors: Record<keyof T, string[]>;
  warnings: Record<keyof T, string[]>;
  info: Record<keyof T, string[]>;
  isValid: boolean;
  isDirty: boolean;
  touched: Record<keyof T, boolean>;

  // Methods
  setValue: <K extends keyof T>(field: K, value: T[K]) => void;
  setTouched: <K extends keyof T>(field: K, touched?: boolean) => void;
  validate: <K extends keyof T>(field?: K) => boolean;
  validateAll: () => boolean;
  reset: () => void;
  setValues: (values: Partial<T>) => void;

  // Field helpers
  getFieldProps: <K extends keyof T>(
    field: K
  ) => {
    value: T[K];
    onChangeText: (value: T[K]) => void;
    onBlur: () => void;
    error: string | undefined;
    warning: string | undefined;
  };
}

/**
 * Smart validation hook with progressive disclosure
 */
export function useSmartValidation<T extends Record<string, unknown>>(
  initialValues: T,
  validationSchema: Partial<Record<keyof T, ValidationRule<unknown>[]>>,
  options?: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    showErrorsOnTouch?: boolean;
    debounceMs?: number;
  }
): UseSmartValidationReturn<T> {
  const {
    validateOnChange = true,
    validateOnBlur = true,
    showErrorsOnTouch = true,
    debounceMs = 300,
  } = options || {};

  const [values, setValuesState] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>);
  const [warnings, setWarnings] = useState<Record<keyof T, string[]>>(
    {} as Record<keyof T, string[]>
  );
  const [info, setInfo] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>);
  const [touched, setTouchedState] = useState<Record<keyof T, boolean>>(
    {} as Record<keyof T, boolean>
  );
  const [isDirty, setIsDirty] = useState(false);

  const debounceTimers = useRef<Record<string, number>>({});
  const _previousValues = useRef<T>(initialValues);

  // Validate a single field
  const validateField = useCallback(
    <K extends keyof T>(field: K): boolean => {
      const fieldRules = validationSchema[field] || [];
      const value = values[field];

      const fieldErrors: string[] = [];
      const fieldWarnings: string[] = [];
      const fieldInfo: string[] = [];

      fieldRules.forEach(rule => {
        if (!rule.validate(value)) {
          switch (rule.severity) {
            case 'warning':
              fieldWarnings.push(rule.message);
              break;
            case 'info':
              fieldInfo.push(rule.message);
              break;
            default:
              fieldErrors.push(rule.message);
          }
        }
      });

      setErrors(prev => ({ ...prev, [field]: fieldErrors }));
      setWarnings(prev => ({ ...prev, [field]: fieldWarnings }));
      setInfo(prev => ({ ...prev, [field]: fieldInfo }));

      return fieldErrors.length === 0;
    },
    [values, validationSchema]
  );

  // Validate all fields
  const validateAll = useCallback((): boolean => {
    let isValid = true;

    Object.keys(validationSchema).forEach(field => {
      const fieldValid = validateField(field as keyof T);
      if (!fieldValid) isValid = false;
    });

    return isValid;
  }, [validationSchema, validateField]);

  // Set a single value with optional validation
  const setValue = useCallback(
    <K extends keyof T>(field: K, value: T[K]) => {
      setValuesState(prev => {
        const newValues = { ...prev, [field]: value };

        // Mark as dirty if value changed
        if (!isDirty && JSON.stringify(newValues) !== JSON.stringify(initialValues)) {
          setIsDirty(true);
        }

        // Validate on change with debounce
        if (validateOnChange) {
          if (debounceTimers.current[field as string]) {
            clearTimeout(debounceTimers.current[field as string]);
          }

          debounceTimers.current[field as string] = setTimeout(() => {
            validateField(field);
          }, debounceMs);
        }

        return newValues;
      });
    },
    [isDirty, initialValues, validateOnChange, validateField, debounceMs]
  );

  // Set multiple values
  const setValues = useCallback(
    (newValues: Partial<T>) => {
      Object.entries(newValues).forEach(([field, value]) => {
        setValue(field as keyof T, value);
      });
    },
    [setValue]
  );

  // Mark field as touched
  const setTouched = useCallback(
    <K extends keyof T>(field: K, touched = true) => {
      setTouchedState(prev => ({ ...prev, [field]: touched }));

      // Validate on blur
      if (validateOnBlur && touched) {
        validateField(field);
      }
    },
    [validateOnBlur, validateField]
  );

  // Reset form
  const reset = useCallback(() => {
    setValuesState(initialValues);
    setErrors({} as Record<keyof T, string[]>);
    setWarnings({} as Record<keyof T, string[]>);
    setInfo({} as Record<keyof T, string[]>);
    setTouchedState({} as Record<keyof T, boolean>);
    setIsDirty(false);

    // Clear all timers
    Object.values(debounceTimers.current).forEach(timer => clearTimeout(timer));
    debounceTimers.current = {};
  }, [initialValues]);

  // Get field props for easy integration
  const getFieldProps = useCallback(
    <K extends keyof T>(field: K) => {
      const fieldErrors = errors[field] || [];
      const fieldWarnings = warnings[field] || [];
      const shouldShowError = showErrorsOnTouch ? touched[field] : true;

      return {
        value: values[field],
        onChangeText: (value: T[K]) => setValue(field, value),
        onBlur: () => setTouched(field, true),
        error: shouldShowError && fieldErrors.length > 0 ? fieldErrors[0] : undefined,
        warning: shouldShowError && fieldWarnings.length > 0 ? fieldWarnings[0] : undefined,
      };
    },
    [values, errors, warnings, touched, showErrorsOnTouch, setValue, setTouched]
  );

  // Calculate overall form validity
  const isValid = Object.values(errors).every(
    fieldErrors => (fieldErrors as string[]).length === 0
  );

  // Haptic feedback on validation errors
  useEffect(() => {
    const hasErrors = Object.values(errors).some(
      fieldErrors => (fieldErrors as string[]).length > 0
    );

    if (hasErrors && isDirty) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
  }, [errors, isDirty]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      Object.values(debounceTimers.current).forEach(timer => clearTimeout(timer));
    };
  }, []);

  return {
    values,
    errors,
    warnings,
    info,
    isValid,
    isDirty,
    touched,
    setValue,
    setTouched,
    validate: validateField,
    validateAll,
    reset,
    setValues,
    getFieldProps,
  };
}

/**
 * Common validation rules
 */
export const validators = {
  required: (message = 'Este campo es obligatorio'): ValidationRule<unknown> => ({
    validate: (value: unknown) => {
      if (typeof value === 'string') return value.trim().length > 0;
      if (Array.isArray(value)) return value.length > 0;
      return value != null && value !== '';
    },
    message,
  }),

  minLength: (min: number, message?: string): ValidationRule<string> => ({
    validate: (value: string) => !value || value.length >= min,
    message: message || `Mínimo ${min} caracteres`,
  }),

  maxLength: (max: number, message?: string): ValidationRule<string> => ({
    validate: (value: string) => !value || value.length <= max,
    message: message || `Máximo ${max} caracteres`,
  }),

  email: (message = 'Email inválido'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    message,
  }),

  phone: (message = 'Teléfono inválido'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      const phoneRegex = /^[\d\s\-\+\(\)]+$/;
      return phoneRegex.test(value) && value.replace(/\D/g, '').length >= 9;
    },
    message,
  }),

  number: (message = 'Debe ser un número'): ValidationRule<unknown> => ({
    validate: (value: unknown) => {
      if (!value) return true;
      return !isNaN(Number(value));
    },
    message,
  }),

  min: (min: number, message?: string): ValidationRule<number> => ({
    validate: (value: number) => value == null || value >= min,
    message: message || `Mínimo ${min}`,
  }),

  max: (max: number, message?: string): ValidationRule<number> => ({
    validate: (value: number) => value == null || value <= max,
    message: message || `Máximo ${max}`,
  }),

  pattern: (regex: RegExp, message: string): ValidationRule<string> => ({
    validate: (value: string) => !value || regex.test(value),
    message,
  }),

  custom: <T = unknown>(
    validate: (value: T) => boolean,
    message: string,
    severity?: 'error' | 'warning' | 'info'
  ): ValidationRule<T> => ({
    validate,
    message,
    severity,
  }),

  // Special validators for Salonier
  percentage: (message = 'Debe ser un porcentaje entre 0 y 100'): ValidationRule<number> => ({
    validate: (value: number) => value == null || (value >= 0 && value <= 100),
    message,
  }),

  futureDate: (message = 'La fecha debe ser futura'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      const date = new Date(value);
      return date > new Date();
    },
    message,
  }),

  pastDate: (message = 'La fecha debe ser pasada'): ValidationRule<string> => ({
    validate: (value: string) => {
      if (!value) return true;
      const date = new Date(value);
      return date < new Date();
    },
    message,
  }),
};
