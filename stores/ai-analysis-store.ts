import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/lib/supabase";
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from '@supabase/supabase-js';

// Result for a single zone
interface ZoneAnalysisResult {
  zone: string;
  depthLevel: number; // Now with decimals
  tone: string;
  undertone: string;
  state: string;
  grayPercentage?: number;
  porosity: string;
  elasticity: string;
  resistance: string;
  damage: string;
  unwantedTone?: string; // Detected unwanted tone
  confidence: number;
  // Nuevos campos profesionales
  grayType?: string; // Tipo de cana
  grayPattern?: string; // Patrón de distribución
  pigmentAccumulation?: string; // Acumulación de pigmentos
  cuticleState?: string; // Estado de la cutícula
  demarkationBands?: { location: number; contrast: string }[]; // Bandas de demarcación
}

// Complete AI analysis result
interface AIAnalysisResult {
  // General characteristics
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  averageDepthLevel: number;
  
  // Zone-specific analysis
  zoneAnalysis: {
    roots: ZoneAnalysisResult;
    mids: ZoneAnalysisResult;
    ends: ZoneAnalysisResult;
  };
  
  // Chemical history detection
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedHomeRemedies?: boolean;
  
  // Risk detection
  detectedRisks?: {
    metalSalts: { detected: boolean; confidence: number; signs: string[] };
    henna: { detected: boolean; confidence: number; signs: string[] };
    extremeDamage: { detected: boolean; zones: string[] };
  };
  
  // Service complexity
  serviceComplexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number; // minutes
  
  // Overall assessment
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
  analysisTimestamp: number;
}

// Photo analysis result for desired color photos
interface DesiredPhotoAnalysis {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number;
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
}

interface AIAnalysisSettings {
  autoFaceBlur: boolean;
  imageQualityThreshold: number;
  privacyMode: boolean;
  saveAnalysisHistory: boolean;
}

interface AIAnalysisState {
  // Analysis state
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResult | null;
  analysisHistory: AIAnalysisResult[];
  
  // Desired photo analysis
  isAnalyzingDesiredPhoto: boolean;
  desiredPhotoAnalyses: Record<string, DesiredPhotoAnalysis>;
  
  // Settings
  settings: AIAnalysisSettings;
  privacyMode: boolean;
  
  // Actions
  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (photoId: string, imageUri: string, currentLevel: number) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// AI analysis function using Supabase Edge Function with base64
const performAIAnalysis = async (imageUri: string): Promise<AIAnalysisResult> => {
  try {
    // 1. Comprimir la imagen para análisis de diagnóstico
    const compressedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [{ resize: { width: 400 } }], // Reducido aún más para evitar stack overflow
      { 
        compress: 0.5, 
        format: ImageManipulator.SaveFormat.JPEG,
        base64: true // Obtener directamente en base64
      }
    );

    // 2. Validar tamaño (máximo ~500KB para edge functions)
    const sizeInBytes = (compressedImage.base64.length * 3) / 4;
    if (sizeInBytes > 500000) {
      console.warn('Image size after compression:', sizeInBytes);
      // Intentar comprimir más si es necesario
      const recompressed = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 600 } }],
        { compress: 0.5, format: ImageManipulator.SaveFormat.JPEG, base64: true }
      );
      compressedImage.base64 = recompressed.base64;
    }

    console.log('Sending image for diagnosis, size:', (compressedImage.base64.length * 3) / 4, 'bytes');

    // 3. Llamar directamente a la Edge Function con base64
    let { data, error } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'diagnose_image',
        payload: {
          imageBase64: compressedImage.base64
        }
      }
    });

    if (error) {
      const errorMessage = await handleEdgeFunctionError(error);

      // Si es un error de tamaño, intentar con imagen más pequeña
      if (errorMessage.includes('payload') || errorMessage.includes('size') || errorMessage.includes('413')) {
        console.log('Image might be too large, retrying with smaller size...');

        const smallerImage = await ImageManipulator.manipulateAsync(
          imageUri,
          [{ resize: { width: 300 } }],
          { compress: 0.3, format: ImageManipulator.SaveFormat.JPEG, base64: true }
        );
        console.log('Retrying with smaller image, size:', (smallerImage.base64.length * 3) / 4, 'bytes');

        const { data: retryData, error: retryError } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            task: 'diagnose_image',
            payload: {
              imageBase64: smallerImage.base64
            }
          }
        });

        if (retryError) {
          const retryErrorMessage = await handleEdgeFunctionError(retryError);
          console.log('Edge Function failed, using fallback data...');
          data = getFallbackDiagnosisData();
        } else {
          data = retryData;
        }
      } else if (errorMessage.includes('504') || errorMessage.includes('timeout') || errorMessage.includes('Gateway')) {
        // Error 504 o timeout - usar datos de respaldo
        console.log('Edge Function timeout detected, using fallback data...');
        data = getFallbackDiagnosisData();
      } else {
        console.log('Edge Function error, using fallback data...');
        data = getFallbackDiagnosisData();
      }
    }

    // Log para debugging
    console.log('Edge Function response:', JSON.stringify(data, null, 2));

    // 4. Validar y devolver resultado
    if (!data) {
      console.error('No data received from Edge Function');
      throw new Error('No se recibió respuesta del servidor');
    }
    
    console.log('Edge Function response details:', { 
      success: data.success, 
      hasData: !!data.data, 
      error: data.error,
      dataType: data.data ? typeof data.data : 'null',
      keys: data.data ? Object.keys(data.data) : []
    });
    
    // Check for the specific case of success: true with data: null
    if (data.success === true && data.data === null) {
      console.error('Edge Function returned success with null data - likely OpenAI API issue');
      throw new Error('Error del servidor: No se pudo procesar la imagen. Verifica que la API de OpenAI esté configurada correctamente.');
    }
    
    if (!data.success) {
      console.error('Edge Function returned error:', data.error);
      throw new Error(`Error en el análisis: ${data.error || 'Error desconocido'}`);
    }
    
    if (!data.data) {
      console.error('Edge Function response missing data field:', data);
      throw new Error('Respuesta del servidor incompleta');
    }

    // Validate essential fields
    const result = data.data;
    if (!result.hairThickness || !result.hairDensity || !result.zoneAnalysis) {
      console.error('AI response missing required fields:', {
        hasHairThickness: !!result.hairThickness,
        hasHairDensity: !!result.hairDensity,
        hasZoneAnalysis: !!result.zoneAnalysis,
        fullResponse: result
      });
      throw new Error('El análisis de IA está incompleto. Por favor, intenta con otra foto más clara.');
    }

    return {
      ...result,
      analysisTimestamp: Date.now()
    };
  } catch (error) {
    console.error('Error in performAIAnalysis:', error);
    throw error;
  }
};

// Función de datos de respaldo cuando la Edge Function falla
function getFallbackDiagnosisData() {
  return {
    success: true,
    data: {
      hairThickness: "medium",
      hairDensity: "normal",
      zoneAnalysis: {
        roots: {
          level: 6,
          tone: "neutral",
          condition: "healthy",
          porosity: "normal",
          coverage: "100%"
        },
        mid: {
          level: 6,
          tone: "neutral",
          condition: "healthy",
          porosity: "normal",
          coverage: "100%"
        },
        ends: {
          level: 6,
          tone: "neutral",
          condition: "healthy",
          porosity: "normal",
          coverage: "100%"
        }
      },
      averageLevel: 6,
      dominantTone: "neutral",
      overallCondition: "healthy",
      recommendations: ["⚠️ Análisis temporal - El servicio de IA está temporalmente deshabilitado. Por favor, realiza el diagnóstico manualmente."],
      isAIGenerated: false,
      analysisTimestamp: Date.now()
    }
  };
}

// Función de datos de respaldo para análisis de fotos deseadas
function getFallbackDesiredPhotoData() {
  return {
    success: true,
    data: {
      targetLevel: 7,
      targetTone: "neutral",
      technique: "global",
      difficulty: "medium",
      timeEstimate: "2-3 horas",
      recommendations: ["⚠️ Análisis temporal - El servicio de IA está temporalmente deshabilitado. Por favor, analiza la foto manualmente."],
      isAIGenerated: false
    }
  };
}

// Helper function to handle Edge Function errors
async function handleEdgeFunctionError(error: any): Promise<string> {
  console.error('Edge Function Error:', error);
  
  if (error instanceof FunctionsHttpError) {
    try {
      const errorData = await error.context.json();
      console.error('Function returned error:', errorData);
      return errorData.error || errorData.message || 'Error en el servidor';
    } catch (e) {
      console.error('Could not parse error response:', e);
      return 'Error al procesar la respuesta del servidor';
    }
  } else if (error instanceof FunctionsRelayError) {
    console.error('Relay error:', error.message);
    return 'Error de conexión con el servidor';
  } else if (error instanceof FunctionsFetchError) {
    console.error('Fetch error:', error.message);
    return 'Error al conectar con el servidor';
  } else {
    return error.message || 'Error desconocido';
  }
}

// Function to analyze desired color photos using Supabase Edge Function with base64
const analyzeDesiredColorPhoto = async (photoId: string, imageUri: string, currentLevel: number): Promise<DesiredPhotoAnalysis> => {
  try {
    // 1. Comprimir la imagen para color deseado (menos detalle necesario)
    const compressedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [{ resize: { width: 350 } }], // Más pequeño para color deseado
      { 
        compress: 0.4, 
        format: ImageManipulator.SaveFormat.JPEG,
        base64: true // Obtener directamente en base64
      }
    );

    // 2. Validar tamaño
    const sizeInBytes = (compressedImage.base64.length * 3) / 4;
    if (sizeInBytes > 400000) {
      console.warn('Desired photo size after compression:', sizeInBytes);
      // Comprimir más si es necesario
      const recompressed = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 500 } }],
        { compress: 0.5, format: ImageManipulator.SaveFormat.JPEG, base64: true }
      );
      compressedImage.base64 = recompressed.base64;
    }

    console.log('Sending desired photo for analysis, size:', (compressedImage.base64.length * 3) / 4, 'bytes');

    // 3. Llamar directamente a la Edge Function con base64
    let { data, error } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'analyze_desired_look',
        payload: {
          imageBase64: compressedImage.base64,
          currentLevel: currentLevel
        }
      }
    });

    if (error) {
      const errorMessage = await handleEdgeFunctionError(error);
      
      // Si es un error de tamaño, intentar con imagen más pequeña
      if (errorMessage.includes('payload') || errorMessage.includes('size') || errorMessage.includes('413')) {
        console.log('Image might be too large, retrying with smaller size...');
        
        const smallerImage = await ImageManipulator.manipulateAsync(
          imageUri,
          [{ resize: { width: 250 } }],
          { compress: 0.3, format: ImageManipulator.SaveFormat.JPEG, base64: true }
        );
        console.log('Retrying with smaller image, size:', (smallerImage.base64.length * 3) / 4, 'bytes');
        
        const { data: retryData, error: retryError } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            task: 'analyze_desired_look',
            payload: {
              imageBase64: smallerImage.base64,
              currentLevel: currentLevel
            }
          }
        });
        
        if (retryError) {
          const retryErrorMessage = await handleEdgeFunctionError(retryError);
          console.log('Desired photo analysis failed, using fallback data...');
          data = getFallbackDesiredPhotoData();
        } else {
          data = retryData;
        }
      } else if (errorMessage.includes('504') || errorMessage.includes('timeout') || errorMessage.includes('Gateway')) {
        // Error 504 o timeout - usar datos de respaldo
        console.log('Desired photo analysis timeout detected, using fallback data...');
        data = getFallbackDesiredPhotoData();
      } else {
        console.log('Desired photo analysis error, using fallback data...');
        data = getFallbackDesiredPhotoData();
      }
    }

    // Log para debugging
    console.log('Edge Function response for desired color:', data);

    // 4. Validar y devolver resultado
    if (!data) {
      throw new Error('No se recibió respuesta del servidor');
    }
    
    if (!data.success) {
      throw new Error(`Error en el análisis: ${data.error || 'Error desconocido'}`);
    }
    
    if (!data.data) {
      throw new Error('Respuesta inválida del servidor: datos faltantes');
    }

    return {
      photoId,
      ...data.data
    };
  } catch (error) {
    console.error('Error in analyzeDesiredColorPhoto:', error);
    throw error;
  }
};

export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  // Initial state
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false, // Default to false for privacy
  },

  // Actions
  analyzeImage: async (imageUri: string) => {
    set({ isAnalyzing: true, analysisResult: null });
    
    try {
      // In production, this would:
      // 1. Upload image to Supabase Storage (temporary bucket)
      // 2. Trigger Edge Function for analysis
      // 3. Edge Function would blur faces and analyze with OpenAI
      // 4. Return results and delete images
      
      const result = await performAIAnalysis(imageUri);
      console.log("AI Analysis Store - Result received:", result);
      
      set(state => ({
        isAnalyzing: false,
        analysisResult: result,
        analysisHistory: state.settings.saveAnalysisHistory 
          ? [...state.analysisHistory, result]
          : state.analysisHistory
      }));
      
      // Save to AsyncStorage if history is enabled
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem(
            'salonier-analysis-history', 
            JSON.stringify(analysisHistory)
          );
        } catch (error) {
          console.error('Error saving analysis history:', error);
        }
      }
      
    } catch (error: any) {
      set({ isAnalyzing: false });
      console.error('Error in analyzeImage:', error);
      
      // Proporcionar mensaje de error más específico
      if (error.message === 'Error al subir la imagen') {
        throw new Error('No se pudo subir la imagen. Por favor, verifica tu conexión a internet.');
      } else if (error.message === 'Error al analizar la imagen') {
        throw new Error('No se pudo analizar la imagen. Por favor, intenta de nuevo.');
      } else {
        throw new Error('Error al procesar la imagen. Por favor, intenta con otra foto.');
      }
    }
  },

  analyzeDesiredPhoto: async (photoId: string, imageUri: string, currentLevel: number) => {
    set({ isAnalyzingDesiredPhoto: true });
    
    try {
      const analysis = await analyzeDesiredColorPhoto(photoId, imageUri, currentLevel);
      
      set(state => ({
        isAnalyzingDesiredPhoto: false,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis
        }
      }));
      
      return analysis;
    } catch (error) {
      set({ isAnalyzingDesiredPhoto: false });
      console.error('Error analyzing desired photo:', error);
      return null;
    }
  },

  clearAnalysis: () => {
    set({ analysisResult: null, desiredPhotoAnalyses: {} });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });
    
    try {
      await AsyncStorage.setItem(
        'salonier-ai-settings', 
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      console.error('Error saving AI settings:', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      console.error('Error clearing analysis history:', error);
    }
  }
}));

// Initialize store with saved data
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }
    
    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    console.error('Error initializing AI analysis store:', error);
  }
};

// Call initialization
initializeAIAnalysisStore();