import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Permission } from "@/types/permissions";
import { supabase } from "@/lib/supabase";
import { Session, User as SupabaseUser } from "@supabase/supabase-js";
import { useSyncQueueStore } from "./sync-queue-store";
import { useInventoryStore } from "./inventory-store";
import { useClientStore } from "./client-store";
import { useClientHistoryStore } from "./client-history-store";
import { useTeamStore } from "./team-store";
import { useSalonConfigStore } from "./salon-config-store";
import { logger } from "@/utils/logger";

interface User {
  id: string;
  email: string;
  name: string;
  isOwner: boolean;
  permissions?: Permission[];
  salonId: string;
}

export interface BrandLineSelection {
  brandId: string;
  selectedLines: string[]; // Array of line IDs
}

interface AuthState {
  isAuthenticated: boolean;
  session: Session | null;
  user: User | null;
  preferredBrandLines: BrandLineSelection[];
  isLoading: boolean;
  setIsAuthenticated: (value: boolean) => void;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string, salonName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updatePreferredBrandLines: (brandLines: BrandLineSelection[]) => Promise<void>;
  addBrandLineSelection: (brandId: string, lineIds: string[]) => Promise<void>;
  removeBrandLineSelection: (brandId: string) => Promise<void>;
  initializeAuth: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const authLogger = logger.withContext('AuthStore');

/**
 * Ensure user has salon_id in their profile
 * Auto-repairs profiles missing salon_id
 */
async function ensureUserHasSalonId(userId: string): Promise<string | null> {
  authLogger.info('Checking user salon_id...');
  
  try {
    // First check if user already has salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', userId)
      .single();

    if (profile?.salon_id) {
      authLogger.info('User already has salon_id', { salonId: profile.salon_id });
      return profile.salon_id;
    }

    // If not, try to find their salon through team members
    authLogger.warn('User profile missing salon_id, attempting to repair...');
    
    const { data: teamMember } = await supabase
      .from('team_members')
      .select('salon_id')
      .eq('user_id', userId)
      .single();

    if (teamMember?.salon_id) {
      // Update profile with found salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: teamMember.salon_id })
        .eq('id', userId);

      if (updateError) {
        authLogger.error('Failed to update profile with salon_id', updateError);
        return null;
      }

      authLogger.info('Profile repaired with salon_id', { salonId: teamMember.salon_id });
      return teamMember.salon_id;
    }

    // As last resort, check if user owns a salon
    const { data: salon } = await supabase
      .from('salons')
      .select('id')
      .eq('owner_id', userId)
      .single();

    if (salon?.id) {
      // Update profile with owned salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: salon.id })
        .eq('id', userId);

      if (updateError) {
        authLogger.error('Failed to update profile with owned salon_id', updateError);
        return null;
      }

      authLogger.info('Profile repaired with owned salon_id', { salonId: salon.id });
      return salon.id;
    }

    authLogger.error('Could not find salon_id for user');
    return null;
  } catch (error) {
    authLogger.error('Error ensuring user has salon_id', error);
    return null;
  }
}

/**
 * Load user preferences from Supabase with fallback to local storage
 */
async function loadPreferencesFromSupabase(salonId: string): Promise<BrandLineSelection[]> {
  try {
    const { data: salon } = await supabase
      .from('salons')
      .select('settings')
      .eq('id', salonId)
      .single();

    if (salon?.settings?.preferredBrandLines) {
      // Load complete brand and line selections
      authLogger.info('Preferences loaded from Supabase with lines', { count: salon.settings.preferredBrandLines.length });
      return salon.settings.preferredBrandLines;
    } else if (salon?.settings?.preferredBrands) {
      // Fallback for old data format - just brand IDs
      const brandLines = salon.settings.preferredBrands.map((brandId: string) => ({
        brandId,
        selectedLines: [] // No line data in old format
      }));
      authLogger.info('Preferences loaded from Supabase (legacy format)', { count: brandLines.length });
      return brandLines;
    }
  } catch (error) {
    authLogger.info('Could not load preferences from Supabase, using local storage');
  }

  // Fallback to local storage
  try {
    const preferences = await AsyncStorage.getItem("salonier-preferences");
    if (preferences) {
      const { brandLines } = JSON.parse(preferences);
      authLogger.info('Preferences loaded from local storage', { count: brandLines.length });
      return brandLines;
    }
  } catch (error) {
    authLogger.error('Error loading preferences from local storage', error);
  }

  return [];
}

/**
 * Sync all stores after authentication
 */
async function syncAllStores(): Promise<void> {
  authLogger.startTimer('syncAllStores');
  
  try {
    await Promise.all([
      useInventoryStore.getState().syncWithSupabase(),
      useClientStore.getState().syncWithSupabase(),
      useTeamStore.getState().syncWithSupabase(),
      useSalonConfigStore.getState().syncWithSupabase(),
    ]);

    // Process any pending sync queue items
    const { processSyncQueue } = useSyncQueueStore.getState();
    processSyncQueue();
    
    authLogger.endTimer('syncAllStores');
  } catch (error) {
    authLogger.error('Error syncing stores', error);
    throw error;
  }
}

/**
 * Wait for user profile to be created after signup
 */
async function waitForProfileCreation(userId: string, maxAttempts: number = 20): Promise<any> {
  authLogger.info('Waiting for profile creation...', { userId, maxAttempts });
  
  let attempts = 0;
  let lastError = null;

  while (attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s between attempts
    
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*, salons(name)')
        .eq('id', userId)
        .single();

      if (profileError) {
        authLogger.debug(`Profile not ready yet (attempt ${attempts + 1})`, { code: profileError.code });
        lastError = profileError;
      } else if (profile && profile.salon_id) {
        authLogger.info('Profile and salon created successfully');
        return profile;
      }
    } catch (err) {
      authLogger.error(`Error checking profile on attempt ${attempts + 1}:`, err);
      lastError = err;
    }
    
    attempts++;
  }

  authLogger.error('Profile creation via trigger failed', { attempts, lastError });
  return null;
}

/**
 * Attempt manual profile setup when automatic trigger fails
 */
async function attemptManualProfileSetup(userId: string, userEmail: string, userName: string): Promise<any> {
  authLogger.info('Attempting manual profile creation via function...');
  
  try {
    const { data: setupResult, error: setupError } = await supabase
      .rpc('manual_user_setup', {
        user_id: userId,
        user_email: userEmail,
        user_name: userName
      });

    if (setupError) {
      authLogger.error('Manual setup function failed:', setupError);
      throw setupError;
    }

    if (setupResult) {
      authLogger.info('Manual setup function succeeded');
      
      // Now fetch the created profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*, salons(name)')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        throw new Error('Profile created but could not be fetched');
      }

      return profile;
    } else {
      throw new Error('Manual setup function returned false');
    }
  } catch (error) {
    authLogger.error('Manual profile creation also failed:', error);
    throw new Error('No se pudo crear el perfil. Por favor, intenta iniciar sesión manualmente.');
  }
}

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false,
  session: null,
  user: null,
  preferredBrandLines: [],
  isLoading: true,
  
  setIsAuthenticated: (value) => set({ isAuthenticated: value }),
  
  signIn: async (email, password) => {
    authLogger.startTimer('signIn');
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.session && data.user) {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*, salons(name)')
          .eq('id', data.user.id)
          .single();

        if (profile) {
          // Ensure user has salon_id before proceeding
          let salonId = profile.salon_id;
          if (!salonId) {
            salonId = await ensureUserHasSalonId(data.user.id);
            if (!salonId) {
              throw new Error('Usuario no asociado a ningún salón. Contacta al administrador.');
            }
          }

          const user: User = {
            id: data.user.id,
            email: data.user.email!,
            name: profile.full_name || data.user.email!,
            isOwner: profile.role === 'owner',
            permissions: profile.permissions as Permission[],
            salonId: salonId,
          };

          set({ 
            isAuthenticated: true, 
            session: data.session,
            user 
          });

          // Load preferences
          const brandLines = await loadPreferencesFromSupabase(user.salonId);
          set({ preferredBrandLines: brandLines });

          // Sync all stores
          await syncAllStores();
        }
      }
      
      authLogger.endTimer('signIn');
    } catch (error) {
      authLogger.error("Error signing in:", error);
      throw error;
    }
  },

  signUp: async (email, password, fullName, salonName) => {
    authLogger.startTimer('signUp');
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            salon_name: salonName || `${fullName}'s Salon`,
          },
        },
      });

      if (error) throw error;

      if (data.user && data.session) {
        authLogger.info('User created, waiting for profile creation...');
        
        // Wait for profile to be created by trigger
        let profile = await waitForProfileCreation(data.user.id);
        
        // If trigger failed, try manual setup
        if (!profile) {
          profile = await attemptManualProfileSetup(data.user.id, data.user.email!, fullName);
        }
        
        // Create user object and authenticate
        const user: User = {
          id: data.user.id,
          email: data.user.email!,
          name: profile.full_name || fullName,
          isOwner: profile.role === 'owner' || true,
          permissions: profile.permissions as Permission[],
          salonId: profile.salon_id,
        };

        set({ 
          isAuthenticated: true, 
          session: data.session,
          user 
        });
        
        authLogger.endTimer('signUp');
      }
    } catch (error) {
      authLogger.error("Error signing up:", error);
      throw error;
    }
  },

  signOut: async () => {
    authLogger.startTimer('signOut');
    
    try {
      // Clear sync queue before signing out
      useSyncQueueStore.getState().clearQueue();
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      await AsyncStorage.removeItem("salonier-preferences");
      set({ 
        isAuthenticated: false, 
        session: null,
        user: null, 
        preferredBrandLines: [] 
      });
      
      authLogger.endTimer('signOut');
    } catch (error) {
      authLogger.error("Error signing out:", error);
      throw error;
    }
  },

  updatePreferredBrandLines: async (brandLines) => {
    try {
      const { user } = get();
      
      // Save to local storage
      await AsyncStorage.setItem("salonier-preferences", JSON.stringify({ brandLines }));
      set({ preferredBrandLines: brandLines });
      
      // Save to Supabase if user is authenticated
      if (user?.salonId) {
        const { data: salon } = await supabase
          .from('salons')
          .select('settings')
          .eq('id', user.salonId)
          .single();

        const currentSettings = salon?.settings || {};
        const updatedSettings = {
          ...currentSettings,
          preferredBrands: brandLines.map(bl => bl.brandId), // Keep for backwards compatibility
          preferredBrandLines: brandLines // Store complete selection with lines
        };

        await supabase
          .from('salons')
          .update({ 
            settings: updatedSettings,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.salonId);
      }
      
      authLogger.info('Preferences updated', { count: brandLines.length });
    } catch (error) {
      authLogger.error("Error storing preferences:", error);
      throw error;
    }
  },

  addBrandLineSelection: async (brandId, lineIds) => {
    authLogger.startTimer('addBrandLineSelection');
    
    const { preferredBrandLines, user } = get();
    if (!user?.salonId) return;

    const existingIndex = preferredBrandLines.findIndex(bl => bl.brandId === brandId);
    
    let newBrandLines;
    if (existingIndex >= 0) {
      // Update existing brand selection
      newBrandLines = [...preferredBrandLines];
      newBrandLines[existingIndex] = { brandId, selectedLines: lineIds };
    } else {
      // Add new brand selection
      newBrandLines = [...preferredBrandLines, { brandId, selectedLines: lineIds }];
    }
    
    // Save to Supabase salon settings
    try {
      const { data: salon } = await supabase
        .from('salons')
        .select('settings')
        .eq('id', user.salonId)
        .single();

      const currentSettings = salon?.settings || {};
      const updatedSettings = {
        ...currentSettings,
        preferredBrands: newBrandLines.map(bl => bl.brandId),
        preferredBrandLines: newBrandLines // Store complete selection with lines
      };

      await supabase
        .from('salons')
        .update({ 
          settings: updatedSettings,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.salonId);

      // Also save to local storage for offline access
      await AsyncStorage.setItem("salonier-preferences", JSON.stringify({ brandLines: newBrandLines }));
      set({ preferredBrandLines: newBrandLines });
      
      authLogger.endTimer('addBrandLineSelection');
    } catch (error) {
      authLogger.error("Error saving brand preferences:", error);
      throw error;
    }
  },

  removeBrandLineSelection: async (brandId) => {
    authLogger.startTimer('removeBrandLineSelection');
    
    const { preferredBrandLines, user } = get();
    if (!user?.salonId) return;

    const newBrandLines = preferredBrandLines.filter(bl => bl.brandId !== brandId);
    
    // Save to Supabase salon settings
    try {
      const { data: salon } = await supabase
        .from('salons')
        .select('settings')
        .eq('id', user.salonId)
        .single();

      const currentSettings = salon?.settings || {};
      const updatedSettings = {
        ...currentSettings,
        preferredBrands: newBrandLines.map(bl => bl.brandId),
        preferredBrandLines: newBrandLines // Store complete selection with lines
      };

      await supabase
        .from('salons')
        .update({ 
          settings: updatedSettings,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.salonId);

      // Also save to local storage
      await AsyncStorage.setItem("salonier-preferences", JSON.stringify({ brandLines: newBrandLines }));
      set({ preferredBrandLines: newBrandLines });
      
      authLogger.endTimer('removeBrandLineSelection');
    } catch (error) {
      authLogger.error("Error removing brand preference:", error);
      throw error;
    }
  },

  initializeAuth: async () => {
    authLogger.startTimer('initializeAuth');
    
    try {
      set({ isLoading: true });

      // Check for existing session
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*, salons(name)')
          .eq('id', session.user.id)
          .single();

        if (profile) {
          // Ensure user has salon_id before proceeding
          let salonId = profile.salon_id;
          if (!salonId) {
            salonId = await ensureUserHasSalonId(session.user.id);
            if (!salonId) {
              authLogger.error('User has no salon_id after restore attempt');
              // Don't throw here, just log out the user
              set({ 
                isAuthenticated: false, 
                session: null,
                user: null 
              });
              return;
            }
          }

          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            name: profile.full_name || session.user.email!,
            isOwner: profile.role === 'owner',
            permissions: profile.permissions as Permission[],
            salonId: salonId,
          };

          set({ 
            isAuthenticated: true, 
            session,
            user 
          });

          // Load preferences
          const brandLines = await loadPreferencesFromSupabase(user.salonId);
          set({ preferredBrandLines: brandLines });

          // Sync all stores
          await syncAllStores();
        }
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        authLogger.info('Auth state changed', { event });
        
        if (event === 'SIGNED_IN' && session) {
          // Handle sign in
          const { data: profile } = await supabase
            .from('profiles')
            .select('*, salons(name)')
            .eq('id', session.user.id)
            .single();

          if (profile) {
            // Ensure user has salon_id before proceeding
            let salonId = profile.salon_id;
            if (!salonId) {
              salonId = await ensureUserHasSalonId(session.user.id);
              if (!salonId) {
                authLogger.error('User has no salon_id in auth state change');
                return;
              }
            }

            const user: User = {
              id: session.user.id,
              email: session.user.email!,
              name: profile.full_name || session.user.email!,
              isOwner: profile.role === 'owner',
              permissions: profile.permissions as Permission[],
              salonId: salonId,
            };

            set({ 
              isAuthenticated: true, 
              session,
              user 
            });

            // Sync stores after auth state change
            await syncAllStores();
          }
        } else if (event === 'SIGNED_OUT') {
          // Handle sign out
          set({ 
            isAuthenticated: false, 
            session: null,
            user: null, 
            preferredBrandLines: [] 
          });
        }
      });

      authLogger.endTimer('initializeAuth');
    } catch (error) {
      authLogger.error("Error initializing auth:", error);
    } finally {
      set({ isLoading: false });
    }
  },

  resetPassword: async (email) => {
    authLogger.startTimer('resetPassword');
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
      
      authLogger.endTimer('resetPassword');
    } catch (error) {
      authLogger.error("Error resetting password:", error);
      throw error;
    }
  },
}));