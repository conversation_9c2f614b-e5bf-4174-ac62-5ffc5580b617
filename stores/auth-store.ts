import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Permission } from "@/types/permissions";
import { supabase } from "@/lib/supabase";
import { Session, User as SupabaseUser } from "@supabase/supabase-js";
import { useSyncQueueStore } from "./sync-queue-store";
import { useInventoryStore } from "./inventory-store";
import { useClientStore } from "./client-store";
import { useClientHistoryStore } from "./client-history-store";

interface User {
  id: string;
  email: string;
  name: string;
  isOwner: boolean;
  permissions?: Permission[];
  salonId: string;
}

interface BrandLineSelection {
  brandId: string;
  selectedLines: string[]; // Array of line IDs
}

interface AuthState {
  isAuthenticated: boolean;
  session: Session | null;
  user: User | null;
  preferredBrandLines: BrandLineSelection[];
  isLoading: boolean;
  setIsAuthenticated: (value: boolean) => void;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string, salonName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updatePreferredBrandLines: (brandLines: BrandLineSelection[]) => Promise<void>;
  addBrandLineSelection: (brandId: string, lineIds: string[]) => Promise<void>;
  removeBrandLineSelection: (brandId: string) => Promise<void>;
  initializeAuth: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false,
  session: null,
  user: null,
  preferredBrandLines: [],
  isLoading: true,
  
  setIsAuthenticated: (value) => set({ isAuthenticated: value }),
  
  signIn: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.session && data.user) {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*, salons(name)')
          .eq('id', data.user.id)
          .single();

        if (profile) {
          const user: User = {
            id: data.user.id,
            email: data.user.email!,
            name: profile.full_name || data.user.email!,
            isOwner: profile.role === 'owner',
            permissions: profile.permissions as Permission[],
            salonId: profile.salon_id,
          };

          set({ 
            isAuthenticated: true, 
            session: data.session,
            user 
          });

          // Load preferences
          const preferences = await AsyncStorage.getItem("salonier-preferences");
          if (preferences) {
            const { brandLines } = JSON.parse(preferences);
            set({ preferredBrandLines: brandLines });
          }

          // Sync all stores after successful login
          await Promise.all([
            useInventoryStore.getState().syncWithSupabase(),
            useClientStore.getState().syncWithSupabase(),
            // Client history is loaded per client on demand
            // Team store syncing
            import('@/stores/team-store').then(({ useTeamStore }) => 
              useTeamStore.getState().syncWithSupabase()
            ),
            // Salon config syncing
            import('@/stores/salon-config-store').then(({ useSalonConfigStore }) => 
              useSalonConfigStore.getState().syncWithSupabase()
            ),
          ]);

          // Process any pending sync queue items
          const { processSyncQueue } = useSyncQueueStore.getState();
          processSyncQueue();
        }
      }
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  },

  signUp: async (email, password, fullName, salonName) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            salon_name: salonName || `${fullName}'s Salon`,
          },
        },
      });

      if (error) throw error;

      // Wait for the trigger to create salon and profile
      // We'll poll for the profile to exist before continuing
      if (data.user && data.session) {
        console.log('[signUp] User created, waiting for profile creation...');
        
        const maxAttempts = 20; // Increase attempts
        let attempts = 0;
        let profileExists = false;
        let lastError = null;

        while (attempts < maxAttempts && !profileExists) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s between attempts
          
          try {
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .select('*, salons(name)')
              .eq('id', data.user.id)
              .single();

            if (profileError) {
              console.log(`[signUp] Attempt ${attempts + 1}: Profile not ready yet`, profileError.code);
              lastError = profileError;
            } else if (profile && profile.salon_id) {
              console.log('[signUp] Profile and salon created successfully');
              profileExists = true;
              
              // Profile and salon are ready, now we can authenticate
              const user: User = {
                id: data.user.id,
                email: data.user.email!,
                name: profile.full_name || data.user.email!,
                isOwner: profile.role === 'owner',
                permissions: profile.permissions as Permission[],
                salonId: profile.salon_id,
              };

              set({ 
                isAuthenticated: true, 
                session: data.session,
                user 
              });

              // Don't auto-sync here, let the app decide when to sync
              return;
            }
          } catch (err) {
            console.error(`[signUp] Error checking profile on attempt ${attempts + 1}:`, err);
            lastError = err;
          }
          
          attempts++;
        }

        // If we couldn't find the profile after max attempts, use manual setup
        if (!profileExists) {
          console.error('[signUp] Profile creation via trigger failed after', attempts, 'attempts. Last error:', lastError);
          
          // Try to create the profile using the manual setup function
          try {
            console.log('[signUp] Attempting manual profile creation via function...');
            
            const { data: setupResult, error: setupError } = await supabase
              .rpc('manual_user_setup', {
                user_id: data.user.id,
                user_email: data.user.email!,
                user_name: fullName
              });

            if (setupError) {
              console.error('[signUp] Manual setup function failed:', setupError);
              throw setupError;
            }

            if (setupResult) {
              console.log('[signUp] Manual setup function succeeded');
              
              // Now fetch the created profile
              const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select('*, salons(name)')
                .eq('id', data.user.id)
                .single();

              if (profileError || !profile) {
                throw new Error('Profile created but could not be fetched');
              }

              // Set user state
              const user: User = {
                id: data.user.id,
                email: data.user.email!,
                name: profile.full_name || fullName,
                isOwner: true,
                permissions: profile.permissions as Permission[],
                salonId: profile.salon_id,
              };

              set({ 
                isAuthenticated: true, 
                session: data.session,
                user 
              });

              return;
            } else {
              throw new Error('Manual setup function returned false');
            }
          } catch (manualError) {
            console.error('[signUp] Manual profile creation also failed:', manualError);
            throw new Error('No se pudo crear el perfil. Por favor, intenta iniciar sesión manualmente.');
          }
        }
      }
    } catch (error) {
      console.error("Error signing up:", error);
      throw error;
    }
  },

  signOut: async () => {
    try {
      // Clear sync queue before signing out
      useSyncQueueStore.getState().clearQueue();
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      await AsyncStorage.removeItem("salonier-preferences");
      set({ 
        isAuthenticated: false, 
        session: null,
        user: null, 
        preferredBrandLines: [] 
      });
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  },

  updatePreferredBrandLines: async (brandLines) => {
    try {
      await AsyncStorage.setItem("salonier-preferences", JSON.stringify({ brandLines }));
      set({ preferredBrandLines: brandLines });
    } catch (error) {
      console.error("Error storing preferences:", error);
      throw error;
    }
  },

  addBrandLineSelection: async (brandId, lineIds) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const existingIndex = preferredBrandLines.findIndex(bl => bl.brandId === brandId);
    
    let newBrandLines;
    if (existingIndex >= 0) {
      // Update existing brand selection
      newBrandLines = [...preferredBrandLines];
      newBrandLines[existingIndex] = { brandId, selectedLines: lineIds };
    } else {
      // Add new brand selection
      newBrandLines = [...preferredBrandLines, { brandId, selectedLines: lineIds }];
    }
    
    await updatePreferredBrandLines(newBrandLines);
  },

  removeBrandLineSelection: async (brandId) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const newBrandLines = preferredBrandLines.filter(bl => bl.brandId !== brandId);
    await updatePreferredBrandLines(newBrandLines);
  },

  initializeAuth: async () => {
    try {
      set({ isLoading: true });

      // Check for existing session
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*, salons(name)')
          .eq('id', session.user.id)
          .single();

        if (profile) {
          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            name: profile.full_name || session.user.email!,
            isOwner: profile.role === 'owner',
            permissions: profile.permissions as Permission[],
            salonId: profile.salon_id,
          };

          set({ 
            isAuthenticated: true, 
            session,
            user 
          });

          // Load preferences
          const preferences = await AsyncStorage.getItem("salonier-preferences");
          if (preferences) {
            const { brandLines } = JSON.parse(preferences);
            set({ preferredBrandLines: brandLines });
          }

          // Sync all stores after initialization
          await Promise.all([
            useInventoryStore.getState().syncWithSupabase(),
            useClientStore.getState().syncWithSupabase(),
            // Team store syncing
            import('@/stores/team-store').then(({ useTeamStore }) => 
              useTeamStore.getState().syncWithSupabase()
            ),
            // Salon config syncing
            import('@/stores/salon-config-store').then(({ useSalonConfigStore }) => 
              useSalonConfigStore.getState().syncWithSupabase()
            ),
          ]);

          // Process any pending sync queue items
          const { processSyncQueue } = useSyncQueueStore.getState();
          processSyncQueue();
        }
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          // Handle sign in
          const { data: profile } = await supabase
            .from('profiles')
            .select('*, salons(name)')
            .eq('id', session.user.id)
            .single();

          if (profile) {
            const user: User = {
              id: session.user.id,
              email: session.user.email!,
              name: profile.full_name || session.user.email!,
              isOwner: profile.role === 'owner',
              permissions: profile.permissions as Permission[],
              salonId: profile.salon_id,
            };

            set({ 
              isAuthenticated: true, 
              session,
              user 
            });

            // Sync stores after auth state change
            await Promise.all([
              useInventoryStore.getState().syncWithSupabase(),
              useClientStore.getState().syncWithSupabase(),
              // Team store syncing
              import('@/stores/team-store').then(({ useTeamStore }) => 
                useTeamStore.getState().syncWithSupabase()
              ),
              // Salon config syncing
              import('@/stores/salon-config-store').then(({ useSalonConfigStore }) => 
                useSalonConfigStore.getState().syncWithSupabase()
              ),
            ]);

            // Process any pending sync queue items
            const { processSyncQueue } = useSyncQueueStore.getState();
            processSyncQueue();
          }
        } else if (event === 'SIGNED_OUT') {
          // Handle sign out
          set({ 
            isAuthenticated: false, 
            session: null,
            user: null, 
            preferredBrandLines: [] 
          });
        }
      });

    } catch (error) {
      console.error("Error initializing auth:", error);
    } finally {
      set({ isLoading: false });
    }
  },

  resetPassword: async (email) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
    } catch (error) {
      console.error("Error resetting password:", error);
      throw error;
    }
  },
}));