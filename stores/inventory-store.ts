import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { useSyncQueueStore, generateLocalId, isLocalId } from './sync-queue-store';
import { Database } from '@/types/database';
import { 
  Product, 
  StockMovement, 
  InventoryAlert, 
  ConsumptionAnalysis,
  InventoryReport 
} from '@/types/inventory';

type SupabaseProduct = Database['public']['Tables']['products']['Row'];
type SupabaseProductInsert = Database['public']['Tables']['products']['Insert'];
type SupabaseProductUpdate = Database['public']['Tables']['products']['Update'];
type SupabaseStockMovement = Database['public']['Tables']['stock_movements']['Row'];
type SupabaseStockMovementInsert = Database['public']['Tables']['stock_movements']['Insert'];

interface InventoryStore {
  products: Product[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  lastSync: string | null;
  isLoading: boolean;
  isInitialized: boolean;
  
  // Product Actions
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'lastUpdated'>) => Promise<string>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  getProductByNameAndBrand: (name: string, brand: string) => Product | undefined;
  searchProducts: (query: string) => Product[];
  
  // Stock Actions
  updateStock: (productId: string, quantity: number, type: StockMovement['type'], reason: string, referenceId?: string) => Promise<void>;
  consumeProducts: (consumptions: Array<{ productId: string; quantity: number }>, referenceId: string, clientName: string) => Promise<void>;
  getStockMovements: (productId?: string, limit?: number) => StockMovement[];
  
  // Alert Actions
  createAlert: (productId: string, type: InventoryAlert['type'], message: string, severity: InventoryAlert['severity']) => void;
  acknowledgeAlert: (alertId: string, userId: string) => void;
  getActiveAlerts: () => InventoryAlert[];
  checkLowStock: () => void;
  
  // Analysis & Reports
  getConsumptionAnalysis: (productId: string, period: 'daily' | 'weekly' | 'monthly') => ConsumptionAnalysis | null;
  generateInventoryReport: () => InventoryReport;
  getProductsByCategory: (category: Product['category']) => Product[];
  getTotalInventoryValue: () => number;
  
  // Sync & Initialize
  initializeWithDefaults: () => void;
  generateMockMovements: () => void;
  clearAllData: () => void;
  syncWithSupabase: () => Promise<void>;
  
  // Migration
  migrateToUnitsSystem: () => void;
}

// Helper functions to convert between local and Supabase formats
function convertSupabaseToLocal(supabaseProduct: SupabaseProduct): Product {
  return {
    id: supabaseProduct.id,
    name: supabaseProduct.name,
    brand: supabaseProduct.brand,
    category: supabaseProduct.category as Product['category'],
    currentStock: supabaseProduct.current_stock,
    minStock: supabaseProduct.min_stock,
    maxStock: supabaseProduct.max_stock || undefined,
    unitType: supabaseProduct.unit_type as Product['unitType'],
    unitSize: supabaseProduct.unit_size,
    purchasePrice: supabaseProduct.purchase_price,
    costPerUnit: supabaseProduct.cost_per_unit,
    barcode: supabaseProduct.barcode || undefined,
    supplier: supabaseProduct.supplier || undefined,
    notes: supabaseProduct.notes || undefined,
    isActive: supabaseProduct.is_active,
    lastUpdated: supabaseProduct.updated_at || new Date().toISOString(),
    _syncStatus: 'synced' as const,
  };
}

function convertLocalToSupabase(product: Product): SupabaseProductInsert {
  return {
    name: product.name,
    brand: product.brand,
    category: product.category,
    current_stock: product.currentStock,
    min_stock: product.minStock,
    max_stock: product.maxStock || null,
    unit_type: product.unitType,
    unit_size: product.unitSize,
    purchase_price: product.purchasePrice,
    cost_per_unit: product.costPerUnit,
    barcode: product.barcode || null,
    supplier: product.supplier || null,
    notes: product.notes || null,
    is_active: product.isActive,
  };
}

function convertSupabaseMovementToLocal(movement: SupabaseStockMovement, product?: Product): StockMovement {
  return {
    id: movement.id,
    productId: movement.product_id,
    type: movement.type as StockMovement['type'],
    quantity: movement.quantity,
    previousStock: movement.previous_stock,
    newStock: movement.new_stock,
    reason: movement.reason,
    referenceId: movement.reference_id || undefined,
    date: movement.created_at,
    createdBy: movement.created_by,
    cost: movement.cost,
    clientName: movement.client_name || undefined,
    _syncStatus: 'synced' as const,
  };
}

function convertLocalMovementToSupabase(movement: StockMovement): SupabaseStockMovementInsert {
  return {
    product_id: movement.productId,
    type: movement.type,
    quantity: movement.quantity,
    previous_stock: movement.previousStock,
    new_stock: movement.newStock,
    reason: movement.reason,
    reference_id: movement.referenceId || null,
    cost: movement.cost,
    client_name: movement.clientName || null,
  };
}

// Productos predefinidos comunes (mantenemos los mismos)
const defaultProducts: Omit<Product, 'id' | 'lastUpdated'>[] = [
  // Oxidantes
  {
    name: 'Oxidante 10 Vol (3%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 20 Vol (6%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1500,
    minStock: 300,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 30 Vol (9%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 40 Vol (12%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  // Tintes
  {
    name: 'Koleston Perfect',
    brand: 'Wella',
    category: 'tinte',
    currentStock: 20,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.5,
    costPerUnit: 0.125,
    isActive: true,
  },
  {
    name: 'Inoa',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 15,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 8,
    costPerUnit: 0.133,
    isActive: true,
  },
  {
    name: 'Igora Royal',
    brand: 'Schwarzkopf',
    category: 'tinte',
    currentStock: 18,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7,
    costPerUnit: 0.117,
    isActive: true,
  },
  {
    name: 'Majirel',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 25,
    minStock: 8,
    unitType: 'ml',
    unitSize: 50,
    purchasePrice: 6.25,
    costPerUnit: 0.125,
    isActive: true,
  },
  // Decolorantes
  {
    name: 'Polvo Decolorante Azul',
    brand: 'Genérico',
    category: 'decolorante',
    currentStock: 500,
    minStock: 100,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 15,
    costPerUnit: 0.03,
    isActive: true,
  },
  {
    name: 'Blondor Multi Blonde',
    brand: 'Wella',
    category: 'decolorante',
    currentStock: 400,
    minStock: 100,
    unitType: 'g',
    unitSize: 800,
    purchasePrice: 28,
    costPerUnit: 0.035,
    isActive: true,
  },
  {
    name: 'Platinium Plus',
    brand: "L'Oréal",
    category: 'decolorante',
    currentStock: 300,
    minStock: 50,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 22,
    costPerUnit: 0.044,
    isActive: true,
  },
  // Tratamientos
  {
    name: 'Olaplex No.1',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 300,
    minStock: 50,
    unitType: 'ml',
    unitSize: 100,
    purchasePrice: 80,
    costPerUnit: 0.8,
    isActive: true,
  },
  {
    name: 'Olaplex No.2',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 120,
    costPerUnit: 0.24,
    isActive: true,
  },
  {
    name: 'Smartbond Step 1',
    brand: "L'Oréal",
    category: 'tratamiento',
    currentStock: 250,
    minStock: 50,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 45,
    costPerUnit: 0.09,
    isActive: true,
  },
  {
    name: 'Fiberplex No.1',
    brand: 'Schwarzkopf',
    category: 'tratamiento',
    currentStock: 200,
    minStock: 40,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 55,
    costPerUnit: 0.11,
    isActive: true,
  },
  {
    name: 'Wellaplex No.1',
    brand: 'Wella',
    category: 'tratamiento',
    currentStock: 180,
    minStock: 30,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 50,
    costPerUnit: 0.10,
    isActive: true,
  },
  // Otros
  {
    name: 'Champú Silver',
    brand: "L'Oréal",
    category: 'otro',
    currentStock: 800,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1500,
    purchasePrice: 18,
    costPerUnit: 0.012,
    isActive: true,
  },
  {
    name: 'Papel de Aluminio',
    brand: 'Genérico',
    category: 'otro',
    currentStock: 50,
    minStock: 10,
    maxStock: 100,
    unitType: 'unidad',
    unitSize: 1,
    purchasePrice: 12,
    costPerUnit: 12,
    isActive: true,
    notes: 'Rollos de 100 metros',
  },
];

export const useInventoryStore = create<InventoryStore>()(
  persist(
    (set, get) => ({
      products: [],
      movements: [],
      alerts: [],
      lastSync: null,
      isLoading: false,
      isInitialized: false,

      loadProducts: async () => {
        set({ isLoading: true });
        
        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            // Silently return if no salon ID (user not authenticated yet)
            set({ isLoading: false });
            return;
          }

          // Load products
          const { data: productsData, error: productsError } = await supabase
            .from('products')
            .select('*')
            .eq('salon_id', salonId)
            .order('name', { ascending: true });

          if (productsError) throw productsError;

          // Load recent movements
          const { data: movementsData, error: movementsError } = await supabase
            .from('stock_movements')
            .select('*')
            .eq('salon_id', salonId)
            .order('created_at', { ascending: false })
            .limit(100);

          if (movementsError) throw movementsError;

          const localProducts = productsData?.map(convertSupabaseToLocal) || [];
          const localMovements = movementsData?.map(m => convertSupabaseMovementToLocal(m)) || [];
          
          set({ 
            products: localProducts,
            movements: localMovements,
            isLoading: false,
            isInitialized: true,
            lastSync: new Date().toISOString()
          });
          
          // Check for low stock after loading
          get().checkLowStock();
        } catch (error) {
          console.error('Error loading inventory:', error);
          set({ isLoading: false });
        }
      },

      addProduct: async (productData) => {
        const tempId = generateLocalId('product');
        const newProduct: Product = {
          ...productData,
          id: tempId,
          lastUpdated: new Date().toISOString(),
          _syncStatus: 'pending',
          _localId: tempId,
        };
        
        // 1. UI Optimista - Actualizar inmediatamente
        set((state) => ({
          products: [...state.products, newProduct],
        }));
        
        // Check stock levels
        get().checkLowStock();
        
        // 2. Intentar sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            const supabaseData = convertLocalToSupabase(productData);
            const { data, error } = await supabase
              .from('products')
              .insert({
                ...supabaseData,
                salon_id: salonId,
              })
              .select()
              .single();

            if (error) throw error;

            // 3. Actualizar con datos reales de Supabase
            set((state) => ({
              products: state.products.map((p) =>
                p.id === tempId ? convertSupabaseToLocal(data) : p
              ),
            }));
            
            return data.id;
          } catch (error) {
            console.error('Error syncing product:', error);
            // 4. Si falla, agregar a la cola de sincronización
            useSyncQueueStore.getState().addToQueue({
              type: 'create',
              table: 'products',
              data: {
                ...convertLocalToSupabase(productData),
                _tempId: tempId,
              },
            });
            
            // Marcar como error
            set((state) => ({
              products: state.products.map((p) =>
                p.id === tempId ? { ...p, _syncStatus: 'error' } : p
              ),
            }));
          }
        } else {
          // Sin conexión, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'create',
            table: 'products',
            data: {
              ...convertLocalToSupabase(productData),
              _tempId: tempId,
            },
          });
        }
        
        return tempId;
      },

      updateProduct: async (id, updates) => {
        // 1. Actualizar UI inmediatamente
        set((state) => ({
          products: state.products.map((p) =>
            p.id === id
              ? { ...p, ...updates, lastUpdated: new Date().toISOString(), _syncStatus: 'pending' }
              : p
          ),
        }));
        
        // Check stock levels after update
        get().checkLowStock();
        
        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline && !isLocalId(id)) {
          try {
            const product = get().products.find(p => p.id === id);
            if (!product) return;

            const supabaseUpdates = convertLocalToSupabase({ ...product, ...updates });
            const { error } = await supabase
              .from('products')
              .update(supabaseUpdates)
              .eq('id', id);

            if (error) throw error;

            // Marcar como sincronizado
            set((state) => ({
              products: state.products.map((p) =>
                p.id === id ? { ...p, _syncStatus: 'synced' } : p
              ),
            }));
          } catch (error) {
            console.error('Error updating product:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'update',
              table: 'products',
              data: { id, ...updates },
            });
            
            // Marcar como error
            set((state) => ({
              products: state.products.map((p) =>
                p.id === id ? { ...p, _syncStatus: 'error' } : p
              ),
            }));
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'update',
            table: 'products',
            data: { id, ...updates },
          });
        }
      },

      deleteProduct: async (id) => {
        // 1. Actualizar UI inmediatamente
        const deletedProduct = get().products.find(p => p.id === id);
        set((state) => ({
          products: state.products.filter((p) => p.id !== id),
          movements: state.movements.filter((m) => m.productId !== id),
          alerts: state.alerts.filter((a) => a.productId !== id),
        }));
        
        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline && !isLocalId(id)) {
          try {
            const { error } = await supabase
              .from('products')
              .delete()
              .eq('id', id);

            if (error) throw error;
          } catch (error) {
            console.error('Error deleting product:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'delete',
              table: 'products',
              data: { id },
            });
            
            // Restaurar el producto con estado de error
            if (deletedProduct) {
              set((state) => ({
                products: [...state.products, { ...deletedProduct, _syncStatus: 'error' }],
              }));
            }
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          if (!isLocalId(id)) {
            useSyncQueueStore.getState().addToQueue({
              type: 'delete',
              table: 'products',
              data: { id },
            });
          }
        }
      },

      updateStock: async (productId, quantity, type, reason, referenceId) => {
        const product = get().getProduct(productId);
        if (!product) throw new Error('Producto no encontrado');
        
        const previousStock = product.currentStock;
        const newStock = type === 'salida' || type === 'consumo' 
          ? previousStock - Math.abs(quantity)
          : previousStock + Math.abs(quantity);
        
        // Create movement record
        const tempMovementId = generateLocalId('movement');
        const movement: StockMovement = {
          id: tempMovementId,
          productId,
          type,
          quantity: type === 'salida' || type === 'consumo' ? -Math.abs(quantity) : Math.abs(quantity),
          previousStock,
          newStock,
          reason,
          referenceId,
          date: new Date().toISOString(),
          createdBy: 'system', // TODO: Get from auth
          cost: Math.abs(quantity) * product.costPerUnit,
          _syncStatus: 'pending',
          _localId: tempMovementId,
        };
        
        // 1. UI Optimista - Actualizar stock y agregar movimiento
        set((state) => ({
          products: state.products.map((p) =>
            p.id === productId
              ? { ...p, currentStock: newStock, lastUpdated: new Date().toISOString(), _syncStatus: 'pending' }
              : p
          ),
          movements: [movement, ...state.movements],
        }));
        
        // Check stock levels after update
        get().checkLowStock();
        
        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline && !isLocalId(productId)) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            // Update stock in Supabase
            const { error: stockError } = await supabase
              .from('products')
              .update({ current_stock: newStock })
              .eq('id', productId);

            if (stockError) throw stockError;

            // Create movement in Supabase
            const movementData = convertLocalMovementToSupabase(movement);
            const { data: movementResult, error: movementError } = await supabase
              .from('stock_movements')
              .insert({
                ...movementData,
                salon_id: salonId,
              })
              .select()
              .single();

            if (movementError) throw movementError;

            // Actualizar con datos reales
            set((state) => ({
              products: state.products.map((p) =>
                p.id === productId ? { ...p, _syncStatus: 'synced' } : p
              ),
              movements: state.movements.map((m) =>
                m.id === tempMovementId ? convertSupabaseMovementToLocal(movementResult) : m
              ),
            }));
          } catch (error) {
            console.error('Error updating stock:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'create',
              table: 'stock_movements',
              data: {
                ...convertLocalMovementToSupabase(movement),
                _tempId: tempMovementId,
                _relatedUpdate: { productId, newStock }
              },
            });
            
            // Marcar como error
            set((state) => ({
              products: state.products.map((p) =>
                p.id === productId ? { ...p, _syncStatus: 'error' } : p
              ),
              movements: state.movements.map((m) =>
                m.id === tempMovementId ? { ...m, _syncStatus: 'error' } : m
              ),
            }));
          }
        } else {
          // Sin conexión, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'create',
            table: 'stock_movements',
            data: {
              ...convertLocalMovementToSupabase(movement),
              _tempId: tempMovementId,
              _relatedUpdate: { productId, newStock }
            },
          });
        }
      },

      // ... Resto de métodos mantienen la misma lógica pero sin sincronización ...
      
      getProduct: (id) => {
        return get().products.find((p) => p.id === id);
      },

      getProductByNameAndBrand: (name, brand) => {
        const normalizedName = name.toLowerCase().trim();
        const normalizedBrand = brand.toLowerCase().trim();
        
        return get().products.find((p) => 
          p.name.toLowerCase().includes(normalizedName) && 
          p.brand.toLowerCase() === normalizedBrand
        );
      },

      searchProducts: (query) => {
        const normalizedQuery = query.toLowerCase().trim();
        return get().products.filter((p) =>
          p.name.toLowerCase().includes(normalizedQuery) ||
          p.brand.toLowerCase().includes(normalizedQuery) ||
          p.category.toLowerCase().includes(normalizedQuery) ||
          (p.barcode && p.barcode.includes(normalizedQuery))
        );
      },

      consumeProducts: async (consumptions, referenceId, clientName) => {
        for (const { productId, quantity } of consumptions) {
          const movement = get().movements.find(
            m => m.productId === productId && 
            m.referenceId === referenceId && 
            m.type === 'consumo'
          );
          
          if (!movement) {
            await get().updateStock(
              productId, 
              quantity, 
              'consumo', 
              `Consumo en servicio para ${clientName}`,
              referenceId
            );
          }
        }
      },

      getStockMovements: (productId, limit = 50) => {
        let movements = get().movements;
        
        if (productId) {
          movements = movements.filter((m) => m.productId === productId);
        }
        
        return movements.slice(0, limit);
      },

      createAlert: (productId, type, message, severity) => {
        const alert: InventoryAlert = {
          id: generateLocalId('alert'),
          productId,
          type,
          severity,
          message,
          acknowledged: false,
          createdAt: new Date().toISOString(),
        };
        
        set((state) => ({
          alerts: [alert, ...state.alerts],
        }));
      },

      acknowledgeAlert: (alertId, userId) => {
        set((state) => ({
          alerts: state.alerts.map((a) =>
            a.id === alertId
              ? { 
                  ...a, 
                  acknowledged: true, 
                  acknowledgedBy: userId,
                  acknowledgedAt: new Date().toISOString(),
                }
              : a
          ),
        }));
      },

      getActiveAlerts: () => {
        return get().alerts.filter((a) => !a.acknowledged);
      },

      checkLowStock: () => {
        const products = get().products;
        
        products.forEach((product) => {
          if (product.currentStock <= product.minStock && product.isActive) {
            const existingAlert = get().alerts.find(
              (a) => a.productId === product.id && a.type === 'stock_bajo' && !a.acknowledged
            );
            
            if (!existingAlert) {
              get().createAlert(
                product.id,
                'stock_bajo',
                `Stock bajo: ${product.currentStock} ${product.unitType} restantes`,
                product.currentStock === 0 ? 'error' : 'warning'
              );
            }
          }
        });
      },

      getConsumptionAnalysis: (productId, period) => {
        const movements = get().movements.filter(
          (m) => m.productId === productId && m.type === 'consumo'
        );
        
        if (movements.length === 0) return null;
        
        const now = new Date();
        const periodDays = period === 'daily' ? 1 : period === 'weekly' ? 7 : 30;
        const startDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000);
        
        const periodMovements = movements.filter(
          (m) => new Date(m.date) >= startDate
        );
        
        const totalConsumed = periodMovements.reduce(
          (sum, m) => sum + Math.abs(m.quantity),
          0
        );
        
        const averageDaily = totalConsumed / periodDays;
        const product = get().getProduct(productId);
        const daysUntilEmpty = product ? product.currentStock / averageDaily : 0;
        
        return {
          productId,
          period,
          totalConsumed,
          averageDaily,
          projectedMonthly: averageDaily * 30,
          daysUntilEmpty: Math.round(daysUntilEmpty),
          consumptionTrend: 'stable', // TODO: Calculate trend
          lastAnalyzed: new Date().toISOString(),
        };
      },

      generateInventoryReport: () => {
        const products = get().products;
        const movements = get().movements;
        
        const totalValue = products.reduce(
          (sum, p) => sum + (p.currentStock * p.costPerUnit),
          0
        );
        
        const lowStockProducts = products.filter(
          (p) => p.currentStock <= p.minStock && p.isActive
        );
        
        const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const recentMovements = movements.filter(
          (m) => new Date(m.date) >= last30Days
        );
        
        const consumptionValue = recentMovements
          .filter((m) => m.type === 'consumo')
          .reduce((sum, m) => sum + m.cost, 0);
        
        const topConsumedProducts = Object.entries(
          recentMovements
            .filter((m) => m.type === 'consumo')
            .reduce((acc, m) => {
              acc[m.productId] = (acc[m.productId] || 0) + Math.abs(m.quantity);
              return acc;
            }, {} as Record<string, number>)
        )
          .sort(([, a], [, b]) => b - a)
          .slice(0, 5)
          .map(([productId, quantity]) => {
            const product = get().getProduct(productId);
            return product ? { product, quantity } : null;
          })
          .filter(Boolean) as Array<{ product: Product; quantity: number }>;
        
        return {
          generatedAt: new Date().toISOString(),
          totalProducts: products.length,
          totalValue,
          lowStockProducts,
          monthlyConsumptionValue: consumptionValue,
          topConsumedProducts,
          productsByCategory: get().getProductsByCategory('tinte').length > 0 ? {
            tinte: get().getProductsByCategory('tinte').length,
            oxidante: get().getProductsByCategory('oxidante').length,
            decolorante: get().getProductsByCategory('decolorante').length,
            tratamiento: get().getProductsByCategory('tratamiento').length,
            otro: get().getProductsByCategory('otro').length,
          } : {},
        };
      },

      getProductsByCategory: (category) => {
        return get().products.filter((p) => p.category === category);
      },

      getTotalInventoryValue: () => {
        return get().products.reduce(
          (sum, p) => sum + (p.currentStock * p.costPerUnit),
          0
        );
      },

      initializeWithDefaults: () => {
        const productsWithIds = defaultProducts.map((p) => ({
          ...p,
          id: generateLocalId('product'),
          lastUpdated: new Date().toISOString(),
          _syncStatus: 'pending' as const,
        }));
        
        set({ products: productsWithIds });
        get().checkLowStock();
      },

      generateMockMovements: () => {
        const products = get().products;
        const movements: StockMovement[] = [];
        const now = new Date();
        
        // Generate movements for the last 30 days
        for (let i = 0; i < 30; i++) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          
          // Random consumptions
          const numConsumptions = Math.floor(Math.random() * 5) + 1;
          for (let j = 0; j < numConsumptions; j++) {
            const product = products[Math.floor(Math.random() * products.length)];
            if (product) {
              const quantity = Math.floor(Math.random() * 50) + 10;
              movements.push({
                id: generateLocalId('movement'),
                productId: product.id,
                type: 'consumo',
                quantity: -quantity,
                previousStock: product.currentStock + quantity,
                newStock: product.currentStock,
                reason: 'Servicio de coloración',
                date: date.toISOString(),
                createdBy: 'system',
                cost: quantity * product.costPerUnit,
                clientName: `Cliente ${Math.floor(Math.random() * 100)}`,
              });
            }
          }
          
          // Random purchases (less frequent)
          if (Math.random() > 0.8) {
            const product = products[Math.floor(Math.random() * products.length)];
            if (product) {
              const quantity = product.unitSize;
              movements.push({
                id: generateLocalId('movement'),
                productId: product.id,
                type: 'entrada',
                quantity: quantity,
                previousStock: product.currentStock - quantity,
                newStock: product.currentStock,
                reason: 'Compra de reposición',
                date: date.toISOString(),
                createdBy: 'system',
                cost: product.purchasePrice,
              });
            }
          }
        }
        
        set((state) => ({
          movements: [...movements, ...state.movements],
        }));
      },

      clearAllData: () => {
        set({
          products: [],
          movements: [],
          alerts: [],
          lastSync: null,
        });
      },

      syncWithSupabase: async () => {
        const { isInitialized } = get();
        if (!isInitialized) {
          await get().loadProducts();
        }
      },

      migrateToUnitsSystem: () => {
        // Esta función ya no es necesaria con el nuevo sistema
        console.log('Migration to units system not needed in new implementation');
      },
    }),
    {
      name: 'inventory-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        products: state.products.filter(p => p._syncStatus !== 'synced'),
        movements: state.movements.filter(m => m._syncStatus !== 'synced'),
        alerts: state.alerts,
      }),
    }
  )
);

// Auto-sync removed - will be triggered after authentication