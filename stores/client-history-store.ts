import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase, getCurrentSalonId } from "@/lib/supabase";
import { useSyncQueueStore, generateLocalId, isLocalId } from "./sync-queue-store";
import { Database } from "@/types/database";

// Tipos locales (mantenemos los mismos)
export interface ClientAllergy {
  substance: string;
  severity: 'leve' | 'moderada' | 'severa';
  dateDetected: string;
  notes?: string;
}

export interface ClientPreference {
  category: 'tono' | 'marca' | 'tecnica' | 'tiempo';
  value: string;
  priority: 'alta' | 'media' | 'baja';
}

export interface HairEvolution {
  date: string;
  level: string;
  porosity: string;
  damage: string;
  resistance: string;
  notes: string;
}

export interface PreviousFormula {
  id: string;
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface PatchTest {
  id: string;
  date: string;
  products: string[];
  result: 'negativo' | 'positivo' | 'pendiente';
  notes?: string;
  reminderSent: boolean;
}

export interface ConsentRecord {
  id: string;
  date: string;
  consentItems: Array<{
    id: string;
    title: string;
    description: string;
    accepted: boolean;
  }>;
  signature: string;
  safetyChecklist: Array<{
    id: string;
    title: string;
    checked: boolean;
  }>;
  ipAddress: string;
  userAgent: string;
  skipSafetyVerification?: boolean;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface ClientHistoryProfile {
  clientId: string;
  allergies: ClientAllergy[];
  preferences: ClientPreference[];
  hairEvolution: HairEvolution[];
  previousFormulas: PreviousFormula[];
  patchTests: PatchTest[];
  consentRecords: ConsentRecord[];
  riskLevel: 'bajo' | 'medio' | 'alto';
  lastAnalysisDate?: string;
  totalServices: number;
  averageSatisfaction: number;
  preferredBrands: string[];
  avoidedIngredients: string[];
  specialNotes: string[];
}

type SupabaseService = Database['public']['Tables']['services']['Row'];
type SupabaseServiceInsert = Database['public']['Tables']['services']['Insert'];
type SupabaseFormula = Database['public']['Tables']['formulas']['Row'];
type SupabaseFormulaInsert = Database['public']['Tables']['formulas']['Insert'];
type SupabaseConsent = Database['public']['Tables']['client_consents']['Row'];
type SupabaseConsentInsert = Database['public']['Tables']['client_consents']['Insert'];

interface ClientHistoryState {
  clientProfiles: Record<string, ClientHistoryProfile>;
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  loadClientHistory: (clientId: string) => Promise<void>;
  getClientProfile: (clientId: string) => ClientHistoryProfile | null;
  updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => void;
  addAllergy: (clientId: string, allergy: ClientAllergy) => void;
  addPreference: (clientId: string, preference: ClientPreference) => void;
  addHairEvolution: (clientId: string, evolution: HairEvolution) => void;
  addPreviousFormula: (clientId: string, formula: PreviousFormula) => Promise<void>;
  addPatchTest: (clientId: string, test: PatchTest) => void;
  addConsentRecord: (clientId: string, consent: ConsentRecord) => Promise<void>;
  getRecommendationsForClient: (clientId: string) => string[];
  getWarningsForClient: (clientId: string) => string[];
  calculateRiskLevel: (clientId: string) => 'bajo' | 'medio' | 'alto';
  getCompatibleFormulas: (clientId: string) => PreviousFormula[];
  getLastPatchTest: (clientId: string) => PatchTest | null;
  getLastConsent: (clientId: string) => ConsentRecord | null;
  initializeClientProfile: (clientId: string) => void;
  syncWithSupabase: () => Promise<void>;
}

// Helper functions for conversion
function convertSupabaseServiceToFormula(service: SupabaseService, formula?: SupabaseFormula): PreviousFormula | null {
  if (!formula || !formula.formula_text) return null;
  
  return {
    id: formula.id,
    date: service.service_date,
    formula: formula.formula_text,
    brand: formula.brand || '',
    line: formula.line || '',
    result: (service.satisfaction_score >= 4 ? 'excelente' : 
             service.satisfaction_score >= 3 ? 'bueno' : 
             service.satisfaction_score >= 2 ? 'regular' : 'malo') as PreviousFormula['result'],
    satisfaction: service.satisfaction_score || 3,
    notes: service.notes || '',
    processingTime: formula.processing_time || 35,
    oxidantVolume: formula.developer_volume?.toString() || '20',
    _syncStatus: 'synced',
  };
}

function convertSupabaseConsentToLocal(consent: SupabaseConsent): ConsentRecord {
  return {
    id: consent.id,
    date: consent.created_at,
    consentItems: consent.consent_data?.items || [],
    signature: consent.signature || '',
    safetyChecklist: consent.safety_checklist || [],
    ipAddress: consent.ip_address || '',
    userAgent: consent.user_agent || '',
    skipSafetyVerification: consent.skip_safety || false,
    _syncStatus: 'synced',
  };
}

export const useClientHistoryStore = create<ClientHistoryState>()(
  persist(
    (set, get) => ({
      clientProfiles: {},
      isLoading: false,
      isInitialized: false,

      loadClientHistory: async (clientId: string) => {
        set({ isLoading: true });
        
        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            // Silently return if no salon ID (user not authenticated yet)
            set({ isLoading: false });
            return;
          }

          // Cargar servicios y fórmulas del cliente
          const { data: services, error: servicesError } = await supabase
            .from('services')
            .select(`
              *,
              formulas (*)
            `)
            .eq('salon_id', salonId)
            .eq('client_id', clientId)
            .order('service_date', { ascending: false });

          if (servicesError) throw servicesError;

          // Cargar consentimientos del cliente
          const { data: consents, error: consentsError } = await supabase
            .from('client_consents')
            .select('*')
            .eq('salon_id', salonId)
            .eq('client_id', clientId)
            .order('created_at', { ascending: false });

          if (consentsError) throw consentsError;

          // Convertir datos y crear perfil
          const previousFormulas: PreviousFormula[] = [];
          let totalSatisfaction = 0;
          let serviceCount = 0;

          services?.forEach(service => {
            if (service.formulas && service.formulas.length > 0) {
              service.formulas.forEach((formula: any) => {
                const converted = convertSupabaseServiceToFormula(service, formula);
                if (converted) {
                  previousFormulas.push(converted);
                  totalSatisfaction += converted.satisfaction;
                  serviceCount++;
                }
              });
            }
          });

          const consentRecords = consents?.map(convertSupabaseConsentToLocal) || [];

          // Obtener cliente para datos adicionales
          const { data: clientData } = await supabase
            .from('clients')
            .select('allergies, medical_conditions, tags')
            .eq('id', clientId)
            .single();

          const allergies: ClientAllergy[] = [];
          if (clientData?.allergies) {
            clientData.allergies.forEach((allergy: string) => {
              allergies.push({
                substance: allergy,
                severity: 'moderada',
                dateDetected: new Date().toISOString(),
              });
            });
          }

          // Crear o actualizar perfil
          const profile: ClientHistoryProfile = {
            clientId,
            allergies,
            preferences: [],
            hairEvolution: [],
            previousFormulas,
            patchTests: [],
            consentRecords,
            riskLevel: get().calculateRiskLevel(clientId),
            lastAnalysisDate: services?.[0]?.service_date,
            totalServices: serviceCount,
            averageSatisfaction: serviceCount > 0 ? totalSatisfaction / serviceCount : 0,
            preferredBrands: [],
            avoidedIngredients: [],
            specialNotes: [],
          };

          set(state => ({
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: profile
            },
            isLoading: false,
            isInitialized: true
          }));
        } catch (error) {
          console.error('Error loading client history:', error);
          set({ isLoading: false });
        }
      },

      getClientProfile: (clientId: string) => {
        return get().clientProfiles[clientId] || null;
      },

      updateClientProfile: (clientId: string, profileUpdate: Partial<ClientHistoryProfile>) => {
        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: {
              ...state.clientProfiles[clientId],
              ...profileUpdate
            }
          }
        }));
      },

      addAllergy: (clientId: string, allergy: ClientAllergy) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                allergies: [...profile.allergies, allergy],
                riskLevel: get().calculateRiskLevel(clientId)
              }
            }
          };
        });

        // TODO: Sincronizar con Supabase actualizando el campo allergies del cliente
      },

      addPreference: (clientId: string, preference: ClientPreference) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                preferences: [...profile.preferences, preference]
              }
            }
          };
        });
      },

      addHairEvolution: (clientId: string, evolution: HairEvolution) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                hairEvolution: [...profile.hairEvolution, evolution].sort((a, b) => 
                  new Date(b.date).getTime() - new Date(a.date).getTime()
                )
              }
            }
          };
        });
      },

      addPreviousFormula: async (clientId: string, formula: PreviousFormula) => {
        const tempId = generateLocalId('formula');
        const newFormula = {
          ...formula,
          id: tempId,
          _syncStatus: 'pending' as const,
          _localId: tempId,
        };

        // 1. UI Optimista
        set(state => {
          const profile = state.clientProfiles[clientId] || {
            clientId,
            allergies: [],
            preferences: [],
            hairEvolution: [],
            previousFormulas: [],
            patchTests: [],
            consentRecords: [],
            riskLevel: 'bajo' as const,
            totalServices: 0,
            averageSatisfaction: 0,
            preferredBrands: [],
            avoidedIngredients: [],
            specialNotes: [],
          };

          const updatedFormulas = [...profile.previousFormulas, newFormula];
          const avgSatisfaction = updatedFormulas.reduce((sum, f) => sum + f.satisfaction, 0) / updatedFormulas.length;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                previousFormulas: updatedFormulas,
                totalServices: profile.totalServices + 1,
                averageSatisfaction: avgSatisfaction
              }
            }
          };
        });

        // 2. Sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            // Crear servicio
            const { data: serviceData, error: serviceError } = await supabase
              .from('services')
              .insert({
                salon_id: salonId,
                client_id: clientId,
                service_date: formula.date,
                satisfaction_score: formula.satisfaction,
                notes: formula.notes,
              })
              .select()
              .single();

            if (serviceError) throw serviceError;

            // Crear fórmula asociada
            const { data: formulaData, error: formulaError } = await supabase
              .from('formulas')
              .insert({
                salon_id: salonId,
                service_id: serviceData.id,
                formula_text: formula.formula,
                brand: formula.brand,
                line: formula.line,
                processing_time: formula.processingTime,
                developer_volume: parseInt(formula.oxidantVolume),
              })
              .select()
              .single();

            if (formulaError) throw formulaError;

            // Actualizar con datos reales
            set(state => {
              const profile = state.clientProfiles[clientId];
              if (!profile) return state;

              return {
                clientProfiles: {
                  ...state.clientProfiles,
                  [clientId]: {
                    ...profile,
                    previousFormulas: profile.previousFormulas.map(f =>
                      f.id === tempId ? { ...f, id: formulaData.id, _syncStatus: 'synced' } : f
                    )
                  }
                }
              };
            });
          } catch (error) {
            console.error('Error syncing formula:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'create',
              table: 'services',
              data: {
                client_id: clientId,
                service_date: formula.date,
                satisfaction_score: formula.satisfaction,
                notes: formula.notes,
                _formula: {
                  formula_text: formula.formula,
                  brand: formula.brand,
                  line: formula.line,
                  processing_time: formula.processingTime,
                  developer_volume: parseInt(formula.oxidantVolume),
                },
                _tempId: tempId,
              },
            });

            // Marcar como error
            set(state => {
              const profile = state.clientProfiles[clientId];
              if (!profile) return state;

              return {
                clientProfiles: {
                  ...state.clientProfiles,
                  [clientId]: {
                    ...profile,
                    previousFormulas: profile.previousFormulas.map(f =>
                      f.id === tempId ? { ...f, _syncStatus: 'error' } : f
                    )
                  }
                }
              };
            });
          }
        } else {
          // Sin conexión, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'create',
            table: 'services',
            data: {
              client_id: clientId,
              service_date: formula.date,
              satisfaction_score: formula.satisfaction,
              notes: formula.notes,
              _formula: {
                formula_text: formula.formula,
                brand: formula.brand,
                line: formula.line,
                processing_time: formula.processingTime,
                developer_volume: parseInt(formula.oxidantVolume),
              },
              _tempId: tempId,
            },
          });
        }
      },

      addPatchTest: (clientId: string, test: PatchTest) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                patchTests: [...profile.patchTests, test]
              }
            }
          };
        });
      },

      addConsentRecord: async (clientId: string, consent: ConsentRecord) => {
        const tempId = generateLocalId('consent');
        const newConsent = {
          ...consent,
          id: tempId,
          _syncStatus: 'pending' as const,
          _localId: tempId,
        };

        // 1. UI Optimista
        set(state => {
          const profile = state.clientProfiles[clientId] || {
            clientId,
            allergies: [],
            preferences: [],
            hairEvolution: [],
            previousFormulas: [],
            patchTests: [],
            consentRecords: [],
            riskLevel: 'bajo' as const,
            totalServices: 0,
            averageSatisfaction: 0,
            preferredBrands: [],
            avoidedIngredients: [],
            specialNotes: [],
          };

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                consentRecords: [...profile.consentRecords, newConsent]
              }
            }
          };
        });

        // 2. Sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            const { data, error } = await supabase
              .from('client_consents')
              .insert({
                salon_id: salonId,
                client_id: clientId,
                consent_data: { items: consent.consentItems },
                signature: consent.signature,
                safety_checklist: consent.safetyChecklist,
                skip_safety: consent.skipSafetyVerification || false,
                ip_address: consent.ipAddress,
                user_agent: consent.userAgent,
              })
              .select()
              .single();

            if (error) throw error;

            // Actualizar con datos reales
            set(state => {
              const profile = state.clientProfiles[clientId];
              if (!profile) return state;

              return {
                clientProfiles: {
                  ...state.clientProfiles,
                  [clientId]: {
                    ...profile,
                    consentRecords: profile.consentRecords.map(c =>
                      c.id === tempId ? convertSupabaseConsentToLocal(data) : c
                    )
                  }
                }
              };
            });
          } catch (error) {
            console.error('Error syncing consent:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'create',
              table: 'client_consents',
              data: {
                client_id: clientId,
                consent_data: { items: consent.consentItems },
                signature: consent.signature,
                safety_checklist: consent.safetyChecklist,
                skip_safety: consent.skipSafetyVerification || false,
                ip_address: consent.ipAddress,
                user_agent: consent.userAgent,
                _tempId: tempId,
              },
            });

            // Marcar como error
            set(state => {
              const profile = state.clientProfiles[clientId];
              if (!profile) return state;

              return {
                clientProfiles: {
                  ...state.clientProfiles,
                  [clientId]: {
                    ...profile,
                    consentRecords: profile.consentRecords.map(c =>
                      c.id === tempId ? { ...c, _syncStatus: 'error' } : c
                    )
                  }
                }
              };
            });
          }
        } else {
          // Sin conexión, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'create',
            table: 'client_consents',
            data: {
              client_id: clientId,
              consent_data: { items: consent.consentItems },
              signature: consent.signature,
              safety_checklist: consent.safetyChecklist,
              skip_safety: consent.skipSafetyVerification || false,
              ip_address: consent.ipAddress,
              user_agent: consent.userAgent,
              _tempId: tempId,
            },
          });
        }
      },

      // Resto de métodos mantienen la misma lógica
      getRecommendationsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const recommendations: string[] = [];

        // Basado en fórmulas exitosas
        const successfulFormulas = profile.previousFormulas.filter(f => f.satisfaction >= 4);
        if (successfulFormulas.length > 0) {
          const mostSuccessful = successfulFormulas.sort((a, b) => b.satisfaction - a.satisfaction)[0];
          recommendations.push(`Fórmula exitosa anterior: ${mostSuccessful.brand} ${mostSuccessful.line}`);
        }

        // Basado en evolución del cabello
        if (profile.hairEvolution.length > 1) {
          const latest = profile.hairEvolution[0];
          const previous = profile.hairEvolution[1];
          
          if (latest.damage !== previous.damage) {
            recommendations.push(`Cambio en daño capilar: de ${previous.damage} a ${latest.damage}`);
          }
        }

        // Basado en preferencias
        const highPriorityPrefs = profile.preferences.filter(p => p.priority === 'alta');
        highPriorityPrefs.forEach(pref => {
          recommendations.push(`Preferencia del cliente: ${pref.category} - ${pref.value}`);
        });

        // Basado en tiempo desde último servicio
        if (profile.lastAnalysisDate) {
          const daysSince = Math.floor((Date.now() - new Date(profile.lastAnalysisDate).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince > 42) {
            recommendations.push(`Han pasado ${daysSince} días desde el último servicio. Considerar retoque de raíces.`);
          }
        }

        return recommendations;
      },

      getWarningsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const warnings: string[] = [];

        // Alergias
        profile.allergies.forEach(allergy => {
          if (allergy.severity === 'severa') {
            warnings.push(`⚠️ ALERGIA SEVERA: ${allergy.substance}`);
          } else if (allergy.severity === 'moderada') {
            warnings.push(`⚠️ Alergia moderada: ${allergy.substance}`);
          }
        });

        // Test de parche pendiente
        const pendingTests = profile.patchTests.filter(t => t.result === 'pendiente');
        if (pendingTests.length > 0) {
          warnings.push(`⚠️ Test de parche pendiente desde ${pendingTests[0].date}`);
        }

        // Test de parche positivo reciente
        const recentPositiveTests = profile.patchTests.filter(t => {
          const daysSince = Math.floor((Date.now() - new Date(t.date).getTime()) / (1000 * 60 * 60 * 24));
          return t.result === 'positivo' && daysSince <= 30;
        });
        if (recentPositiveTests.length > 0) {
          warnings.push(`⚠️ Test de parche POSITIVO reciente - NO PROCEDER`);
        }

        // Fórmulas con malos resultados
        const badFormulas = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        if (badFormulas.length > 0) {
          warnings.push(`⚠️ Evitar fórmulas similares a servicios con baja satisfacción`);
        }

        // Ingredientes a evitar
        profile.avoidedIngredients.forEach(ingredient => {
          warnings.push(`⚠️ Evitar: ${ingredient}`);
        });

        // Riesgo alto
        if (profile.riskLevel === 'alto') {
          warnings.push(`⚠️ Cliente de alto riesgo - Extremar precauciones`);
        }

        // Consentimiento vencido
        const lastConsent = get().getLastConsent(clientId);
        if (lastConsent) {
          const daysSince = Math.floor((Date.now() - new Date(lastConsent.date).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince > 365) {
            warnings.push(`⚠️ Consentimiento vencido - Renovar antes de proceder`);
          }
        }

        return warnings;
      },

      calculateRiskLevel: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return 'bajo';

        let riskScore = 0;

        // Alergias severas
        const severeAllergies = profile.allergies.filter(a => a.severity === 'severa');
        riskScore += severeAllergies.length * 3;

        // Alergias moderadas
        const moderateAllergies = profile.allergies.filter(a => a.severity === 'moderada');
        riskScore += moderateAllergies.length * 2;

        // Tests de parche positivos
        const positiveTests = profile.patchTests.filter(t => t.result === 'positivo');
        riskScore += positiveTests.length * 4;

        // Fórmulas con mala satisfacción
        const badFormulas = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        riskScore += badFormulas.length * 1;

        // Clasificar riesgo
        if (riskScore >= 5) return 'alto';
        if (riskScore >= 2) return 'medio';
        return 'bajo';
      },

      getCompatibleFormulas: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        return profile.previousFormulas
          .filter(f => f.satisfaction >= 4)
          .sort((a, b) => b.satisfaction - a.satisfaction);
      },

      getLastPatchTest: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.patchTests.length === 0) return null;

        return profile.patchTests.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
      },

      getLastConsent: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.consentRecords.length === 0) return null;

        return profile.consentRecords.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
      },

      initializeClientProfile: (clientId: string) => {
        if (!get().clientProfiles[clientId]) {
          set(state => ({
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                clientId,
                allergies: [],
                preferences: [],
                hairEvolution: [],
                previousFormulas: [],
                patchTests: [],
                consentRecords: [],
                riskLevel: 'bajo',
                totalServices: 0,
                averageSatisfaction: 0,
                preferredBrands: [],
                avoidedIngredients: [],
                specialNotes: [],
              }
            }
          }));
        }
      },

      syncWithSupabase: async () => {
        // La sincronización se maneja individualmente por cliente cuando se carga
        console.log('Client history sync managed per client on load');
      },
    }),
    {
      name: "client-history-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        clientProfiles: Object.fromEntries(
          Object.entries(state.clientProfiles).map(([clientId, profile]) => [
            clientId,
            {
              ...profile,
              previousFormulas: profile.previousFormulas.filter(f => f._syncStatus !== 'synced'),
              consentRecords: profile.consentRecords.filter(c => c._syncStatus !== 'synced'),
            }
          ])
        ),
      }),
    }
  )
);