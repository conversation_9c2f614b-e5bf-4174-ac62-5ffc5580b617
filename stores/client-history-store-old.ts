import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface ClientAllergy {
  substance: string;
  severity: 'leve' | 'moderada' | 'severa';
  dateDetected: string;
  notes?: string;
}

export interface ClientPreference {
  category: 'tono' | 'marca' | 'tecnica' | 'tiempo';
  value: string;
  priority: 'alta' | 'media' | 'baja';
}

export interface HairEvolution {
  date: string;
  level: string;
  porosity: string;
  damage: string;
  resistance: string;
  notes: string;
}

export interface PreviousFormula {
  id: string;
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
}

export interface PatchTest {
  id: string;
  date: string;
  products: string[];
  result: 'negativo' | 'positivo' | 'pendiente';
  notes?: string;
  reminderSent: boolean;
}

export interface ConsentRecord {
  id: string;
  date: string;
  consentItems: Array<{
    id: string;
    title: string;
    description: string;
    accepted: boolean;
  }>;
  signature: string;
  safetyChecklist: Array<{
    id: string;
    title: string;
    checked: boolean;
  }>;
  ipAddress: string;
  userAgent: string;
  skipSafetyVerification?: boolean;
}

export interface ClientHistoryProfile {
  clientId: string;
  allergies: ClientAllergy[];
  preferences: ClientPreference[];
  hairEvolution: HairEvolution[];
  previousFormulas: PreviousFormula[];
  patchTests: PatchTest[];
  consentRecords: ConsentRecord[];
  riskLevel: 'bajo' | 'medio' | 'alto';
  lastAnalysisDate?: string;
  totalServices: number;
  averageSatisfaction: number;
  preferredBrands: string[];
  avoidedIngredients: string[];
  specialNotes: string[];
}

interface ClientHistoryState {
  clientProfiles: Record<string, ClientHistoryProfile>;
  
  // Actions
  getClientProfile: (clientId: string) => ClientHistoryProfile | null;
  updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => void;
  addAllergy: (clientId: string, allergy: ClientAllergy) => void;
  addPreference: (clientId: string, preference: ClientPreference) => void;
  addHairEvolution: (clientId: string, evolution: HairEvolution) => void;
  addPreviousFormula: (clientId: string, formula: PreviousFormula) => void;
  addPatchTest: (clientId: string, test: PatchTest) => void;
  addConsentRecord: (clientId: string, consent: ConsentRecord) => void;
  getRecommendationsForClient: (clientId: string) => string[];
  getWarningsForClient: (clientId: string) => string[];
  calculateRiskLevel: (clientId: string) => 'bajo' | 'medio' | 'alto';
  getCompatibleFormulas: (clientId: string) => PreviousFormula[];
  getLastPatchTest: (clientId: string) => PatchTest | null;
  getLastConsent: (clientId: string) => ConsentRecord | null;
  initializeClientProfile: (clientId: string) => void;
}

export const useClientHistoryStore = create<ClientHistoryState>()(
  persist(
    (set, get) => ({
      clientProfiles: {},

      getClientProfile: (clientId: string) => {
        return get().clientProfiles[clientId] || null;
      },

      updateClientProfile: (clientId: string, profileUpdate: Partial<ClientHistoryProfile>) => {
        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: {
              ...state.clientProfiles[clientId],
              ...profileUpdate
            }
          }
        }));
      },

      addAllergy: (clientId: string, allergy: ClientAllergy) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                allergies: [...profile.allergies, allergy],
                riskLevel: get().calculateRiskLevel(clientId)
              }
            }
          };
        });
      },

      addPreference: (clientId: string, preference: ClientPreference) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                preferences: [...profile.preferences, preference]
              }
            }
          };
        });
      },

      addHairEvolution: (clientId: string, evolution: HairEvolution) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                hairEvolution: [...profile.hairEvolution, evolution].sort((a, b) => 
                  new Date(b.date).getTime() - new Date(a.date).getTime()
                )
              }
            }
          };
        });
      },

      addPreviousFormula: (clientId: string, formula: PreviousFormula) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          const updatedFormulas = [...profile.previousFormulas, formula];
          const avgSatisfaction = updatedFormulas.reduce((sum, f) => sum + f.satisfaction, 0) / updatedFormulas.length;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                previousFormulas: updatedFormulas,
                totalServices: profile.totalServices + 1,
                averageSatisfaction: avgSatisfaction
              }
            }
          };
        });
      },

      addPatchTest: (clientId: string, test: PatchTest) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                patchTests: [...profile.patchTests, test]
              }
            }
          };
        });
      },

      addConsentRecord: (clientId: string, consent: ConsentRecord) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                consentRecords: [...profile.consentRecords, consent]
              }
            }
          };
        });
      },

      getRecommendationsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const recommendations: string[] = [];

        // Basado en fórmulas exitosas
        const successfulFormulas = profile.previousFormulas.filter(f => f.satisfaction >= 4);
        if (successfulFormulas.length > 0) {
          const mostSuccessful = successfulFormulas.sort((a, b) => b.satisfaction - a.satisfaction)[0];
          recommendations.push(`Fórmula exitosa anterior: ${mostSuccessful.brand} ${mostSuccessful.line}`);
        }

        // Basado en evolución del cabello
        if (profile.hairEvolution.length > 1) {
          const latest = profile.hairEvolution[0];
          const previous = profile.hairEvolution[1];
          
          if (latest.damage !== previous.damage) {
            recommendations.push(`Cambio en daño capilar: de ${previous.damage} a ${latest.damage}`);
          }
        }

        // Basado en preferencias
        const highPriorityPrefs = profile.preferences.filter(p => p.priority === 'alta');
        highPriorityPrefs.forEach(pref => {
          recommendations.push(`Preferencia del cliente: ${pref.category} - ${pref.value}`);
        });

        // Basado en tiempo desde último servicio
        if (profile.lastAnalysisDate) {
          const daysSince = Math.floor((Date.now() - new Date(profile.lastAnalysisDate).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince > 42) {
            recommendations.push(`Han pasado ${daysSince} días desde el último servicio. Considerar retoque de raíces.`);
          }
        }

        return recommendations;
      },

      getWarningsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const warnings: string[] = [];

        // Alergias
        profile.allergies.forEach(allergy => {
          if (allergy.severity === 'severa') {
            warnings.push(`⚠️ ALERGIA SEVERA: ${allergy.substance}`);
          } else if (allergy.severity === 'moderada') {
            warnings.push(`⚠️ Alergia moderada: ${allergy.substance}`);
          }
        });

        // Test de parche pendiente
        const pendingTests = profile.patchTests.filter(t => t.result === 'pendiente');
        if (pendingTests.length > 0) {
          warnings.push(`⚠️ Test de parche pendiente desde ${pendingTests[0].date}`);
        }

        // Test de parche positivo reciente
        const recentPositiveTests = profile.patchTests.filter(t => {
          const daysSince = Math.floor((Date.now() - new Date(t.date).getTime()) / (1000 * 60 * 60 * 24));
          return t.result === 'positivo' && daysSince <= 30;
        });
        if (recentPositiveTests.length > 0) {
          warnings.push(`⚠️ Test de parche POSITIVO reciente - NO PROCEDER`);
        }

        // Fórmulas con malos resultados
        const badFormulas = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        if (badFormulas.length > 0) {
          warnings.push(`⚠️ Evitar fórmulas similares a servicios con baja satisfacción`);
        }

        // Ingredientes a evitar
        profile.avoidedIngredients.forEach(ingredient => {
          warnings.push(`⚠️ Evitar: ${ingredient}`);
        });

        // Riesgo alto
        if (profile.riskLevel === 'alto') {
          warnings.push(`⚠️ Cliente de alto riesgo - Extremar precauciones`);
        }

        // Consentimiento vencido
        const lastConsent = get().getLastConsent(clientId);
        if (lastConsent) {
          const daysSince = Math.floor((Date.now() - new Date(lastConsent.date).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince > 365) {
            warnings.push(`⚠️ Consentimiento vencido - Renovar antes de proceder`);
          }
        }

        return warnings;
      },

      calculateRiskLevel: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return 'bajo';

        let riskScore = 0;

        // Alergias severas
        const severeAllergies = profile.allergies.filter(a => a.severity === 'severa');
        riskScore += severeAllergies.length * 3;

        // Alergias moderadas
        const moderateAllergies = profile.allergies.filter(a => a.severity === 'moderada');
        riskScore += moderateAllergies.length * 2;

        // Servicios con baja satisfacción
        const badServices = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        riskScore += badServices.length;

        // Test de parche positivos
        const positiveTests = profile.patchTests.filter(t => t.result === 'positivo');
        riskScore += positiveTests.length * 2;

        if (riskScore >= 5) return 'alto';
        if (riskScore >= 2) return 'medio';
        return 'bajo';
      },

      getCompatibleFormulas: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        return profile.previousFormulas
          .filter(f => f.satisfaction >= 4)
          .sort((a, b) => b.satisfaction - a.satisfaction);
      },

      getLastPatchTest: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.patchTests.length === 0) return null;

        return profile.patchTests.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
      },

      getLastConsent: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.consentRecords.length === 0) return null;

        return profile.consentRecords.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
      },

      initializeClientProfile: (clientId: string) => {
        const existing = get().clientProfiles[clientId];
        if (existing) return;

        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: {
              clientId,
              allergies: [],
              preferences: [],
              hairEvolution: [],
              previousFormulas: [],
              patchTests: [],
              consentRecords: [],
              riskLevel: 'bajo',
              totalServices: 0,
              averageSatisfaction: 0,
              preferredBrands: [],
              avoidedIngredients: [],
              specialNotes: []
            }
          }
        }));
      }
    }),
    {
      name: 'salonier-client-history',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);