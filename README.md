# Salon Copilot: Asistente de Coloración Capilar con IA

**Versión:** 2.0.8 | **Estado:** Desarrollo 🚧 | **Optimizado:** 2025-01-18

> 💡 **IMPORTANTE**: La aplicación ahora funciona con arquitectura cloud completa usando Supabase. Para configuración inicial, ver [README_SETUP.md](README_SETUP.md)

> 🚀 **PERFORMANCE**: Sistema completamente optimizado con políticas RLS mejoradas, índices optimizados y Edge Functions actualizadas. Ver [SUPABASE_OPTIMIZATION_SUMMARY.md](SUPABASE_OPTIMIZATION_SUMMARY.md)

## 📱 Descripción

Salon Copilot es una aplicación móvil profesional diseñada para coloristas y estilistas que revoluciona el proceso de coloración capilar mediante inteligencia artificial y gestión inteligente de inventario.

## ✨ Características Principales

### 🎨 Asistente de Coloración con IA
- **Diagnóstico Inteligente**: Análisis automático del cabello mediante fotos
- **Fórmulas Ultra-Inteligentes**: Generación de fórmulas personalizadas basadas en IA
- **Análisis de Viabilidad**: Evaluación de riesgos antes de aplicar color
- **Historial de Cliente**: Seguimiento completo de la evolución capilar
- **Mantener Color Actual**: Replica fórmulas de otras marcas cuando el cliente está feliz con su color
- **Corrección de Color**: Detección y pasos automáticos para neutralización y pre-pigmentación
- **Instrucciones Paso a Paso Premium**: Flujo visual de 10 pantallas interactivas para guiar todo el proceso de coloración

### 📦 Sistema de Inventario y Costos
- **Control Multinivel**: 
  - Solo Fórmulas: Sin gestión de inventario
  - Smart Cost: Cálculo de costos reales
  - Control Total: Gestión completa con consumo automático
- **Validación de Stock**: Verificación en tiempo real antes de cada servicio
- **Cálculo de Márgenes**: Análisis de rentabilidad instantáneo
- **Consumo Automático**: Actualización de inventario al completar servicios
- **✅ Sincronización Cloud**: Backup automático y sincronización entre dispositivos
- **✅ Modo Offline**: Funciona sin conexión, sincroniza cuando hay internet

### 📸 Captura y Análisis de Imágenes
- **Cámara Guiada**: Asistente para capturar fotos desde ángulos óptimos
- **Privacidad Garantizada**: Difuminado facial automático
- **Análisis por Zonas**: Raíces, medios y puntas analizados independientemente

### ⚙️ Configuración Simplificada
- **Configuración de IA**: Solo 2 controles esenciales (Nivel de Análisis y Modo Privacidad)
- **Notificaciones Inteligentes**: Recordatorios de servicios y alertas de inventario
- **Gestión de Datos**: Exportación y control total sobre tu información
- **✅ Sistema Multi-Usuario**: Gestión de equipo con 7 permisos granulares
- **✅ Autenticación Cloud**: Login seguro con Supabase Auth
- **✅ Multi-tenancy**: Cada salón con datos completamente aislados

## ⚠️ Limitaciones Conocidas

### Cámara en iOS para Color Deseado
- **Limitación**: En iOS, la fase de "Color Deseado" usa la cámara nativa del sistema en lugar de la cámara guiada.
- **Razón**: Bug conocido en expo-camera cuando se usa dentro de Modal en iOS.
- **Impacto**: Sin guías visuales en iOS para esta fase específica.
- **Solución futura**: Se está investigando una solución para mantener la experiencia completa.
- Ver detalles completos en [`docs/CAMERA_CRASH_INVESTIGATION.md`](docs/CAMERA_CRASH_INVESTIGATION.md)

## 🚀 Instalación

> 💡 **Configuración de Supabase requerida**: Ver [README_SETUP.md](README_SETUP.md) para instrucciones detalladas

### Requisitos Previos
- Node.js 18+ 
- npm o yarn
- Expo CLI (`npm install -g expo-cli`)
- Expo Go app en tu dispositivo móvil

### Pasos de Instalación

1. Clonar el repositorio:
```bash
git clone https://github.com/OscarCortijo/rork-salonier-copilot--asistente-de-coloraci-n-capilar-con-ia.git
cd rork-salonier-copilot--asistente-de-coloraci-n-capilar-con-ia
```

2. Instalar dependencias:
```bash
npm install
```

3. Iniciar la aplicación:
```bash
npx expo start --tunnel
```

4. Escanear el código QR con Expo Go

## 📂 Estructura del Proyecto

```
├── app/                    # Pantallas principales (Expo Router)
│   ├── (tabs)/            # Navegación principal
│   │   ├── index.tsx      # Dashboard
│   │   ├── clients.tsx    # Gestión de clientes
│   │   └── inventory.tsx  # Gestión de inventario
│   ├── service/           # Flujo de servicio
│   │   └── new.tsx        # Nuevo servicio completo
│   └── client/            # Detalles de cliente
├── components/            # Componentes reutilizables
│   ├── inventory/         # Componentes de inventario
│   └── ...               # Otros componentes
├── stores/               # Estado global (Zustand)
│   ├── inventory-store.ts # Gestión de inventario
│   └── salon-config-store.ts # Configuración del salón
├── services/             # Lógica de negocio
│   └── inventoryConsumptionService.ts
└── types/                # Definiciones TypeScript
```

## 🔧 Configuración

### Niveles de Control de Inventario

En la pantalla de configuración, puedes elegir entre:

1. **Solo Fórmulas**: Ideal para freelancers que no necesitan control de inventario
2. **Smart Cost**: Calcula costos reales basados en tu inventario
3. **Control Total**: Gestión completa con consumo automático y alertas de stock

### Configuración de Precios

- Define márgenes de ganancia (0-500%)
- Configura políticas de redondeo
- Establece precio mínimo por servicio
- Gestiona impuestos

## 📱 Uso de la Aplicación

### Nuevo Servicio

1. **Seleccionar Cliente**: Elige o crea un nuevo cliente
2. **Diagnóstico Capilar**: 
   - Captura fotos con guía o usa análisis manual
   - Análisis automático con IA en un solo clic
   - Diagnóstico detallado por zonas (raíces, medios, puntas)
   - Detección de matices no deseados
3. **Resultado Deseado**:
   - Captura referencias con cámara guiada
   - Define técnica y objetivos
   - Análisis de viabilidad automático
4. **Formulación**:
   - Genera fórmula con IA ultra-inteligente
   - **NUEVO**: Conversión automática entre marcas
   - Corrección de color incluida cuando es necesaria
   - Verifica stock disponible en tiempo real
   - Revisa costos y márgenes con precisión
5. **Resultado Final**:
   - Documenta resultado
   - Activa consumo de inventario

### Gestión de Inventario

- **Agregar Productos**: Define productos con precios y stock
- **Movimientos**: Registra compras y ajustes
- **Alertas**: Recibe notificaciones de stock bajo
- **Reportes**: Analiza consumo y rentabilidad

## 🛠️ Tecnologías Utilizadas

- **React Native** con **Expo SDK 51**
- **TypeScript** para type safety
- **Expo Router** para navegación
- **Zustand** para estado global con UI Optimistic
- **Supabase** para backend cloud (PostgreSQL, Auth, Storage, Edge Functions)
- **AsyncStorage** para datos offline no sincronizados
- **Lucide Icons** para iconografía
- **OpenAI API** para análisis de imágenes y generación de fórmulas

## 🤝 Contribución

Este proyecto está en desarrollo activo. Para contribuir:

1. Fork el repositorio
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Proyecto privado - Todos los derechos reservados

## 👥 Equipo

Desarrollado por el equipo de Rork con la asistencia de Claude AI

## 🆕 Última Actualización Mayor - v2.0.0 (2025-07-11)

### ✨ Migración a Arquitectura Cloud:
- **Backend Supabase Completo**: Base de datos PostgreSQL, autenticación, storage y edge functions
- **Sistema Offline-First**: Funciona sin conexión con sincronización automática
- **Multi-Usuario Mejorado**: Gestión de equipo con 7 niveles de permisos
- **Edge Functions para IA**: Procesamiento seguro en servidor
- **Indicadores de Sincronización**: Feedback visual del estado de conexión
- **RLS Multi-tenancy**: Aislamiento completo de datos entre salones

## 🌍 Configuración Regional (2025-07-04)

### Países Soportados
- **Europa**: España 🇪🇸, Francia 🇫🇷, Alemania 🇩🇪, Italia 🇮🇹, Reino Unido 🇬🇧, Portugal 🇵🇹
- **América del Norte**: Estados Unidos 🇺🇸, Canadá 🇨🇦, México 🇲🇽
- **América del Sur**: Brasil 🇧🇷, Argentina 🇦🇷, Chile 🇨🇱, Colombia 🇨🇴

### Características Regionales
- **Sistema de Medición Automático**: Métrico (ml, g) o Imperial (fl oz, oz)
- **Multi-Moneda**: 26 monedas disponibles con conversión automática
- **Formatos Locales**: Fecha, hora, separadores decimales según país
- **Terminología Específica**: Oxidante/Developer, Tinte/Color según región
- **Regulaciones Locales**: Límites de volúmenes de oxidante, pruebas de alergia

### Generación de Fórmulas Regional
- Fórmulas adaptadas al sistema de medición del país
- Instrucciones en el idioma local (ES, EN, PT, FR)
- Conversión automática de unidades
- Cumplimiento de regulaciones por país

### 🐛 Correcciones:
- Navegación post-firma mejorada sin bloqueos
- Validaciones robustas para prevenir errores
- Parseo de fórmulas más flexible
- Mejor manejo de estados asíncronos
- Corrección del error "chemicalDamage" en generación de fórmulas
- Modal de sistema de medidas corregido con contenido visible
- Selector de países con búsqueda integrada

---

## 🛠️ Herramientas de Desarrollo

### Scripts de Mantenimiento
- **`scripts/dev-tools.sh`**: Herramienta interactiva para tareas comunes
  ```bash
  ./scripts/dev-tools.sh
  ```
- **`scripts/monitor-performance.sql`**: Monitoreo de performance de base de datos
- **`scripts/maintenance-tasks.sql`**: Tareas de mantenimiento SQL
- **`scripts/validate-optimizations.sql`**: Validación de optimizaciones aplicadas

### Documentación Técnica
- **[SUPABASE_OPTIMIZATION_SUMMARY.md](SUPABASE_OPTIMIZATION_SUMMARY.md)**: Resumen de optimizaciones aplicadas
- **[docs/SUPABASE_BEST_PRACTICES.md](docs/SUPABASE_BEST_PRACTICES.md)**: Mejores prácticas para Supabase
- **[scripts/supabase-dashboard-setup.md](scripts/supabase-dashboard-setup.md)**: Configuración manual pendiente

### Comandos Útiles
```bash
# Desarrollo local
npm run ios         # Ejecutar en iOS
npm run android     # Ejecutar en Android
npm run web         # Ejecutar en navegador

# Testing y calidad
npm test            # Ejecutar tests
npm run lint        # Verificar código

# Build producción
eas build --platform ios --profile production
eas build --platform android --profile production
```

---

Para más información o soporte, contacta al equipo de desarrollo.