import { ColorFormula } from '@/types/formulation';

/**
 * Parse formula text to extract structured data
 * Handles various formula formats from different brands
 */
export function parseFormulaText(formulaText: string): ColorFormula {
  // Extract brand and line from first line
  const lines = formulaText.split('\n');
  const firstLine = lines[0] || '';
  
  // Try to extract brand and line
  const brandLineMatch = firstLine.match(/^([^:]+):\s*$/);
  const [brand, line] = brandLineMatch 
    ? brandLineMatch[1].split(' ').filter(Boolean)
    : ['Unknown', 'Unknown'];

  // Extract colors/tones
  const colors: ColorFormula['colors'] = [];
  const colorRegex = /[-–]\s*([\w\s]+)\s+(\d+[/.,-]?\d*)\s*\((\d+)g\)/g;
  let colorMatch;
  
  while ((colorMatch = colorRegex.exec(formulaText)) !== null) {
    colors.push({
      tone: colorMatch[2],
      amount: parseInt(colorMatch[3])
    });
  }

  // Extract developer/oxidant info
  const developerMatch = formulaText.match(/[Oo]xidante\s+(\d+)\s*vol/);
  const developerVolume = developerMatch ? parseInt(developerMatch[1]) : 20;
  
  const ratioMatch = formulaText.match(/\(?(1:\d+(?:\.\d+)?)\)?/);
  const developerRatio = ratioMatch ? ratioMatch[1] : "1:1.5";

  // Extract processing time
  const timeMatch = formulaText.match(/(\d+)\s*min/);
  const processingTime = timeMatch ? parseInt(timeMatch[1]) : 35;

  // Extract additives
  const additives: string[] = [];
  if (formulaText.includes('Olaplex')) {
    additives.push('Olaplex');
  }
  if (formulaText.includes('protector') || formulaText.includes('Protector')) {
    additives.push('Protector');
  }

  // Default values if nothing found
  if (colors.length === 0) {
    colors.push({
      tone: '7/1',
      amount: 60
    });
  }

  return {
    brand: brand || 'Unknown',
    line: line || 'Unknown',
    colors,
    developerVolume,
    developerRatio,
    additives,
    processingTime,
    formulaText
  };
}

/**
 * Parse formula text to extract product list as shown in shopping list
 * This extracts the exact products and quantities from the AI-generated formula
 */
export function parseFormulaTextToProducts(formulaText: string): Array<{
  name: string;
  amount: number;
  unit: string;
}> {
  const products: Array<{ name: string; amount: number; unit: string }> = [];
  
  // Split by lines and process each line
  const lines = formulaText.split('\n').map(line => line.trim()).filter(Boolean);
  
  for (const line of lines) {
    // Pattern 1: "60gr Illumina Color 9/03" or "160ml Oxidante 30 vol"
    const match1 = line.match(/(\d+)\s*(gr?|ml)\s+(.+?)(?:\s*\(|$)/);
    if (match1) {
      products.push({
        name: match1[3].trim(),
        amount: parseInt(match1[1]),
        unit: match1[2] === 'gr' ? 'g' : match1[2]
      });
      continue;
    }
    
    // Pattern 2: "- Illumina Color 9/03 (60g)" or "- Oxidante 30 vol (160ml)"
    const match2 = line.match(/^[-–•]\s*(.+?)\s*\((\d+)\s*(g|ml)\)/);
    if (match2) {
      products.push({
        name: match2[1].trim(),
        amount: parseInt(match2[2]),
        unit: match2[3]
      });
      continue;
    }
    
    // Pattern 3: "Illumina Color 9/03: 60g" or "Oxidante 30 vol: 160ml"
    const match3 = line.match(/^(.+?):\s*(\d+)\s*(g|ml)/);
    if (match3) {
      products.push({
        name: match3[1].trim(),
        amount: parseInt(match3[2]),
        unit: match3[3]
      });
      continue;
    }
    
    // Pattern 4: Check for oxidant/developer in various formats
    if (line.toLowerCase().includes('oxidante') || line.toLowerCase().includes('developer')) {
      const volMatch = line.match(/(\d+)\s*vol/);
      const amountMatch = line.match(/(\d+)\s*(ml|g)/);
      if (volMatch && amountMatch) {
        products.push({
          name: `Oxidante ${volMatch[1]} vol`,
          amount: parseInt(amountMatch[1]),
          unit: amountMatch[2]
        });
      }
    }
  }
  
  // If no products found, try to extract from the parsed formula structure
  if (products.length === 0) {
    const formula = parseFormulaText(formulaText);
    
    // Add color products
    formula.colors.forEach(color => {
      products.push({
        name: `${formula.brand} ${formula.line} ${color.tone}`.trim(),
        amount: color.amount || 0,
        unit: 'g'
      });
    });
    
    // Add developer
    if (formula.developerVolume) {
      const totalColorAmount = formula.colors.reduce((sum, c) => sum + (c.amount || 0), 0);
      const ratioMatch = formula.developerRatio?.match(/1:(\d+\.?\d*)/);
      const multiplier = ratioMatch ? parseFloat(ratioMatch[1]) : 1.5;
      const developerAmount = Math.round(totalColorAmount * multiplier);
      
      products.push({
        name: `Oxidante ${formula.developerVolume} vol`,
        amount: developerAmount,
        unit: 'ml'
      });
    }
    
    // Add additives
    formula.additives.forEach(additive => {
      products.push({
        name: additive,
        amount: 10,
        unit: 'ml'
      });
    });
  }
  
  return products;
}

/**
 * Calculate simple formula cost without inventory
 * Used as fallback when inventory is not available
 */
export function calculateSimpleFormulaCost(formulaText: string) {
  // Use the same parser to extract products
  const products = parseFormulaTextToProducts(formulaText);
  
  // Simple cost estimates
  const costPerGramTint = 0.15; // €0.15 per gram
  const costPerMlDeveloper = 0.005; // €0.005 per ml
  const additiveCost = 2.5; // €2.50 per additive
  
  let totalCost = 0;
  const items = [];
  
  for (const product of products) {
    let unitCost = 0;
    let cost = 0;
    
    // Determine cost based on product type
    if (product.name.toLowerCase().includes('oxidante') || 
        product.name.toLowerCase().includes('developer')) {
      unitCost = costPerMlDeveloper;
      cost = product.amount * unitCost;
    } else if (product.name.toLowerCase().includes('olaplex') || 
               product.name.toLowerCase().includes('protector')) {
      unitCost = additiveCost;
      cost = additiveCost;
    } else {
      // Default to tint cost
      unitCost = product.unit === 'ml' ? costPerMlDeveloper : costPerGramTint;
      cost = product.amount * unitCost;
    }
    
    totalCost += cost;
    items.push({
      product: product.name,
      amount: `${product.amount}${product.unit}`,
      unitCost: unitCost,
      totalCost: cost
    });
  }
  
  // Apply standard markup (3x material cost)
  const suggestedServicePrice = totalCost * 3;
  const profitMargin = suggestedServicePrice - totalCost;
  
  return {
    items,
    totalMaterialCost: Math.round(totalCost * 100) / 100,
    suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
    profitMargin: Math.round(profitMargin * 100) / 100
  };
}