import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Package, Edit, Trash2, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { Product } from '@/types/inventory';

interface InventoryListItemProps {
  item: Product;
  canManageInventory: boolean;
  canViewCosts: boolean;
  formatCurrency: (amount: number) => string;
  formatVolume: (amount: number) => string;
  formatWeight: (amount: number) => string;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  onDelete: (id: string, name: string) => void;
}

const InventoryListItem = React.memo<InventoryListItemProps>(({
  item,
  canManageInventory,
  canViewCosts,
  formatCurrency,
  formatVolume,
  formatWeight,
  getUnitLabel,
  onDelete
}) => {
  const isLowStock = item.currentStock <= item.minStock;

  const handleStock = () => {
    router.push(`/inventory/${item.id}`);
  };

  const handleEdit = () => {
    router.push(`/inventory/edit/${item.id}`);
  };

  return (
    <View style={[styles.productCard, isLowStock && styles.lowStockCard]}>
      <View style={styles.cardHeader}>
        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.brandCategoryRow}>
            <Text style={styles.productBrand}>{item.brand}</Text>
            <Text style={styles.separator}> • </Text>
            <Text style={styles.productCategory}>
              {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
            </Text>
          </View>
        </View>
        {isLowStock && (
          <View style={styles.lowStockBadge}>
            <AlertCircle size={16} color={Colors.light.warning} />
          </View>
        )}
      </View>
      
      <View style={styles.cardDetails}>
        <View style={styles.stockInfo}>
          <Text style={styles.detailLabel}>Stock:</Text>
          <Text style={[styles.detailValue, isLowStock && styles.lowStockText]}>
            {item.unitType === 'ml' || item.unitType === 'fl oz' 
              ? formatVolume(item.currentStock)
              : formatWeight(item.currentStock)}
            {item.unitSize && item.currentStock >= item.unitSize && (
              <Text style={styles.packageCount}>
                {' '}({Math.floor(item.currentStock / item.unitSize)} envases)
              </Text>
            )}
          </Text>
        </View>
        {canViewCosts && (
          <View style={styles.priceInfo}>
            <Text style={styles.detailLabel}>Precio:</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(item.costPerUnit)}/{getUnitLabel(item.unitType === 'ml' || item.unitType === 'fl oz' ? 'volume' : 'weight')}
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.cardActions}>
        <TouchableOpacity 
          style={[styles.cardButton, styles.stockButton]}
          onPress={handleStock}
        >
          <Package size={16} color={Colors.light.primary} />
          <Text style={styles.cardButtonText}>Stock</Text>
        </TouchableOpacity>
        {canManageInventory && (
          <>
            <TouchableOpacity 
              style={[styles.cardButton, styles.editButton]}
              onPress={handleEdit}
            >
              <Edit size={16} color={Colors.light.primary} />
              <Text style={styles.cardButtonText}>Editar</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.cardButton, styles.deleteButton]} 
              onPress={() => onDelete(item.id, item.name)}
            >
              <Trash2 size={16} color={Colors.light.error} />
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.item.name === nextProps.item.name &&
    prevProps.item.brand === nextProps.item.brand &&
    prevProps.item.category === nextProps.item.category &&
    prevProps.item.currentStock === nextProps.item.currentStock &&
    prevProps.item.minStock === nextProps.item.minStock &&
    prevProps.item.costPerUnit === nextProps.item.costPerUnit &&
    prevProps.item.unitSize === nextProps.item.unitSize &&
    prevProps.item.unitType === nextProps.item.unitType &&
    prevProps.canManageInventory === nextProps.canManageInventory &&
    prevProps.canViewCosts === nextProps.canViewCosts
  );
});

InventoryListItem.displayName = 'InventoryListItem';

const styles = StyleSheet.create({
  productCard: {
    backgroundColor: "white",
    borderRadius: radius.lg,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
    borderWidth: 1,
    borderColor: "#F5F5F7",
  },
  lowStockCard: {
    borderColor: Colors.light.warning,
    borderWidth: 1,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.sm,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandCategoryRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  productBrand: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  separator: {
    fontSize: typography.sizes.sm,
    color: Colors.light.lightGray,
  },
  productCategory: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
  },
  lowStockBadge: {
    backgroundColor: Colors.light.warning + "20",
    padding: 6,
    borderRadius: radius.sm,
  },
  cardDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: "#F5F5F7",
  },
  stockInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  priceInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginRight: spacing.xs,
  },
  detailValue: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  lowStockText: {
    color: Colors.light.warning,
    fontWeight: typography.weights.semibold,
  },
  cardActions: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  cardButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.sm,
    borderRadius: radius.sm,
    gap: 6,
  },
  stockButton: {
    backgroundColor: "#F5F5F7",
  },
  editButton: {
    backgroundColor: "#F5F5F7",
  },
  deleteButton: {
    backgroundColor: Colors.light.error + "10",
    flex: 0,
    paddingHorizontal: spacing.md,
  },
  cardButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  packageCount: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.normal,
  },
});

export default InventoryListItem;