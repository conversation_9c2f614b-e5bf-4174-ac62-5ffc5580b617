// Salonier Assistant Edge Function - EMERGENCY STABLE VERSION
// Version: EMERGENCY-STABLE
// Last Updated: 2025-08-23
// Changes: Simplified version to restore service immediately

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

serve(async (req) => {
  console.log('EMERGENCY Edge function invoked:', {
    method: req.method,
    url: req.url
  })
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { task, payload } = await req.json()
    
    console.log('EMERGENCY Request received:', {
      task,
      payloadKeys: Object.keys(payload || {})
    })

    if (!task) {
      return new Response(
        JSON.stringify({ success: false, error: 'Task is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Return mock data for all tasks to restore service
    let result: any
    
    switch (task) {
      case 'diagnose_image':
        result = {
          success: true,
          data: {
            // Campos requeridos por ai-analysis-store.ts
            hairThickness: "medium",
            hairDensity: "normal",
            zoneAnalysis: {
              roots: {
                level: 6,
                tone: "neutral",
                condition: "healthy",
                porosity: "normal",
                coverage: "100%"
              },
              mid: {
                level: 6,
                tone: "neutral",
                condition: "healthy",
                porosity: "normal",
                coverage: "100%"
              },
              ends: {
                level: 6,
                tone: "neutral",
                condition: "healthy",
                porosity: "normal",
                coverage: "100%"
              }
            },
            averageLevel: 6,
            dominantTone: "neutral",
            overallCondition: "healthy",
            recommendations: ["⚠️ Análisis temporal - servicio restaurado. La IA está temporalmente deshabilitada."],
            isAIGenerated: false,
            analysisTimestamp: Date.now()
          }
        }
        break
      case 'analyze_desired_look':
        result = {
          success: true,
          data: {
            targetLevel: 7,
            targetTone: "neutral",
            technique: "global",
            difficulty: "medium",
            timeEstimate: "2-3 horas",
            recommendations: ["Análisis temporal - servicio restaurado"],
            isAIGenerated: false
          }
        }
        break
      case 'generate_formula':
        result = {
          success: true,
          data: {
            formula: [
              {
                product: "Tinte Base",
                amount: "60ml",
                notes: "Fórmula temporal - servicio restaurado"
              },
              {
                product: "Oxidante 20vol",
                amount: "60ml", 
                notes: "Proporción 1:1"
              }
            ],
            technique: "Aplicación global",
            processingTime: "35 minutos",
            isAIGenerated: false
          }
        }
        break
      case 'convert_formula':
        result = {
          success: true,
          data: {
            convertedFormula: [
              {
                product: "Producto Convertido",
                amount: "60ml",
                notes: "Conversión temporal - servicio restaurado"
              }
            ],
            isAIGenerated: false
          }
        }
        break
      case 'parse_product_text':
        result = {
          success: true,
          data: {
            products: [
              {
                name: "Producto Parseado",
                brand: "Marca Temporal",
                type: "color",
                notes: "Parsing temporal - servicio restaurado"
              }
            ],
            isAIGenerated: false
          }
        }
        break
      case 'analyze_product':
        result = {
          success: true,
          data: {
            analysis: "Análisis temporal - servicio restaurado",
            isAIGenerated: false
          }
        }
        break
      default:
        result = { 
          success: true, 
          data: { 
            message: "Servicio restaurado - datos temporales",
            isAIGenerated: false
          } 
        }
    }

    console.log('EMERGENCY Task result:', {
      task,
      success: result.success
    })

    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
    
  } catch (error: any) {
    console.error('EMERGENCY Edge function error:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
